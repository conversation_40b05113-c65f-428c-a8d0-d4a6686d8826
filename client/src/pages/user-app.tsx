import React, { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { LockerMap } from "@/components/user/locker-map";
import { LockerSelection } from "@/components/user/locker-selection";
import { BookingFlow } from "@/components/user/booking-flow";
import { RegistrationForm } from "@/components/user/registration-form";
import { BiometricUnlock } from "@/components/user/biometric-unlock";
import { LockerSelector } from "@/components/user/locker-selector";
import { NotificationCenter } from "@/components/user/notification-center";
import { DateTimePicker } from "@/components/user/DateTimePicker";
import { DeliveryCoordination } from "@/components/user/delivery-coordination";
import { SupportChat } from "@/components/support/support-chat";
import { QRScanner, useQRScanner } from "@/components/mobile/qr-scanner";
import { LocationTracker } from "@/components/mobile/location-tracker";
import { MobileMap } from "@/components/mobile/mobile-map";
import { MobileLayout } from "@/components/mobile/mobile-layout";
import { mobileFeatures } from "@/lib/mobile-features";
import { PremiumSubscription } from "@/components/user/premium-subscription";
import { PremiumFeatures } from "@/components/user/premium-features";
import { PremiumUpgradeModal } from "@/components/user/premium-upgrade-modal";
import { RestrictionModal } from "@/components/common/RestrictionModal";
import { SupportModal } from "@/components/common/SupportModal";
import { subscriptionService } from "@/lib/subscription-service";
import { useQuery } from "@tanstack/react-query";
import { useWebSocket } from "@/hooks/useWebSocket";
import { useNotifications, type Notification } from "@/hooks/useNotifications";
import { useRestrictionMonitor } from "@/hooks/useRestrictionMonitor";
import { queryClient } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { ArrowLeft, Home, MapPin, QrCode, User, UserPlus, Package, Star, Bell, History, Lock, MessageCircle, Fingerprint, Crown, Unlock, X, Phone, Mail, CheckCircle2, Clock, Camera } from "lucide-react";

// Error Boundary Component
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error('UserApp Error Boundary caught an error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen bg-red-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg shadow-lg p-6 max-w-md w-full">
            <h2 className="text-xl font-bold text-red-600 mb-4">Something went wrong</h2>
            <p className="text-gray-600 mb-4">The app encountered an error. Please refresh the page to try again.</p>
            <button
              onClick={() => window.location.reload()}
              className="w-full bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700"
            >
              Refresh Page
            </button>
            <details className="mt-4">
              <summary className="text-sm text-gray-500 cursor-pointer">Error Details</summary>
              <pre className="text-xs text-gray-400 mt-2 overflow-auto">
                {this.state.error?.toString()}
              </pre>
            </details>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

function UserAppContent() {
  const [currentScreen, setCurrentScreen] = useState("login");
  const [userCredentials, setUserCredentials] = useState({ username: "", password: "" });
  const [currentUser, setCurrentUser] = useState(null);
  const [selectedLocker, setSelectedLocker] = useState<{id: number, code: string, bookingId?: number} | null>(null);
  const [selectedLockerData, setSelectedLockerData] = useState<{
    size: string;
    category: string;
    type: string;
    estimatedPrice: number;
  } | null>(null);
  const [showNotifications, setShowNotifications] = useState(false);
  const [showSupportChat, setShowSupportChat] = useState(false);
  const [showDateTimePicker, setShowDateTimePicker] = useState(false);
  const [selectedDateTime, setSelectedDateTime] = useState<{
    startDate: Date;
    endDate: Date;
    duration: number;
  } | null>(null);
  const [pushNotificationsEnabled, setPushNotificationsEnabled] = useState(true);
  const [biometricUnlockEnabled, setBiometricUnlockEnabled] = useState(true);
  const [twoFactorAuthEnabled, setTwoFactorAuthEnabled] = useState(false);
  const [userLocation, setUserLocation] = useState<{ lat: number; lng: number } | null>(null);
  const [showPremiumUpgrade, setShowPremiumUpgrade] = useState(false);
  const [premiumTriggerFeature, setPremiumTriggerFeature] = useState<string>('');
  const [showExpiredLockerDialog, setShowExpiredLockerDialog] = useState<any>(null);
  const [expiredLockerRating, setExpiredLockerRating] = useState(0);
  const [expiredLockerFeedback, setExpiredLockerFeedback] = useState("");
  const [showRestrictionModal, setShowRestrictionModal] = useState(false);
  const [showSupportModal, setShowSupportModal] = useState(false);
  const [restrictionReason, setRestrictionReason] = useState("");
  const [showAgentCoordination, setShowAgentCoordination] = useState(false);
  const [selectedBookingForAgent, setSelectedBookingForAgent] = useState<any>(null);



  // All hooks must be called before any conditional logic
  const { lastMessage } = useWebSocket();
  const { toast } = useToast();
  const { openScanner, ScannerComponent } = useQRScanner();

  // Initialize notifications for the current user
  const {
    notifications,
    unreadCount,
    markAsRead,
    markAllAsRead,
    clearNotification,
    clearAllNotifications
  } = useNotifications((currentUser as any)?.id);

  // Monitor user restriction status
  const { isRestricted } = useRestrictionMonitor({
    userId: (currentUser as any)?.id,
    onRestricted: (reason) => {
      // Only show restriction modal if user is currently logged in (not on login screen)
      if (currentScreen !== "login") {
        setRestrictionReason(reason);
        setShowRestrictionModal(true);
        setCurrentUser(null);
        setCurrentScreen("login");
      }
    }
  });

  // Real-time data queries from database
  const { data: stats } = useQuery({
    queryKey: ["/api/stats"],
    refetchInterval: 10000, // Refresh every 10 seconds
  });

  // All bookings (for admin purposes, not used in user interface)
  // const { data: bookings = [] } = useQuery({
  //   queryKey: ["/api/bookings"],
  //   refetchInterval: 5000,
  //   enabled: !!currentUser,
  // });

  const { data: lockers = [] } = useQuery({
    queryKey: ["/api/lockers"],
    refetchInterval: 5000, // Refresh every 5 seconds
  });

  // Mock data for premium feature testing
  const mockLockers = [
    { id: 1, code: 'A001', size: 'small', status: 'available', isPremium: false },
    { id: 2, code: 'A002', size: 'medium', status: 'available', isPremium: false },
    { id: 3, code: 'P001', size: 'large', status: 'available', isPremium: true },
    { id: 4, code: 'P002', size: 'xl', status: 'available', isPremium: true },
  ];

  const mockBookings = [
    { id: 1, userId: currentUser?.id, lockerId: 1, status: 'active', startTime: Date.now() - 30 * 60 * 1000 },
    { id: 2, userId: currentUser?.id, lockerId: 2, status: 'completed', startTime: Date.now() - 2 * 24 * 60 * 60 * 1000 },
  ];

  const { data: userBookings = [] } = useQuery({
    queryKey: ["/api/bookings/user", (currentUser as any)?.id],
    queryFn: async () => {
      if (!(currentUser as any)?.id) return [];
      const response = await fetch(`/api/bookings/user/${(currentUser as any).id}`);
      if (!response.ok) throw new Error('Failed to fetch user bookings');
      const data = await response.json();
      return data.filter((booking: any) =>
        booking.status === 'active' || booking.status === 'pending'
      );
    },
    refetchInterval: 5000,
    enabled: !!(currentUser as any)?.id,
  });

  const { data: userBookingHistory = [] } = useQuery({
    queryKey: ["/api/bookings/user/history", (currentUser as any)?.id],
    queryFn: async () => {
      if (!(currentUser as any)?.id) return [];
      const response = await fetch(`/api/bookings/user/${(currentUser as any).id}`);
      if (!response.ok) throw new Error('Failed to fetch user booking history');
      const data = await response.json();
      return data
        .filter((booking: any) =>
          booking.status === 'completed' || booking.status === 'cancelled' || booking.status === 'redeemed' || booking.status === 'expired'
        )
        .sort((a: any, b: any) => b.endTime - a.endTime); // Sort by most recent first
    },
    refetchInterval: 10000,
    enabled: !!(currentUser as any)?.id,
  });

  // Handle real-time updates via WebSocket
  useEffect(() => {
    if (lastMessage) {
      try {
        const messageTypes = [
          'locker_status_updated',
          'booking_created',
          'booking_updated',
          'booking_extended',
          'grace_period_adjusted',
          'task_update',
          'new_task',
          'esp32_command',
          'esp32_unlock',
          'payment_processed'
        ];

        if (messageTypes.includes(lastMessage.type)) {
          // Invalidate relevant queries to refresh data
          queryClient.invalidateQueries({ queryKey: ["/api/lockers"] });
          queryClient.invalidateQueries({ queryKey: ["/api/stats"] });
          queryClient.invalidateQueries({ queryKey: ["/api/bookings"] });
          queryClient.invalidateQueries({ queryKey: ["/api/bookings/user", (currentUser as any)?.id] });
          queryClient.invalidateQueries({ queryKey: ["/api/bookings/user/history"] });

          // Show user-relevant notifications
          if (lastMessage.type === 'booking_created' && lastMessage.data?.userId === (currentUser as any)?.id) {
            toast({
              title: "Booking Confirmed",
              description: `Your locker ${lastMessage.data.lockerCode} has been reserved`,
            });
          }

          if (lastMessage.type === 'locker_status_updated' && lastMessage.data?.userId === (currentUser as any)?.id) {
            toast({
              title: "Locker Status Updated",
              description: `Locker ${lastMessage.data.lockerCode} is now ${lastMessage.data.status}`,
            });
          }

          if (lastMessage.type === 'booking_extended' && lastMessage.data?.userId === (currentUser as any)?.id) {
            const extensionMinutes = lastMessage.data.extensionMinutes;
            const hours = Math.floor(extensionMinutes / 60);
            const minutes = extensionMinutes % 60;
            const timeText = hours > 0 ? `${hours}h ${minutes}m` : `${minutes}m`;

            toast({
              title: "Booking Extended",
              description: `Your locker time has been extended by ${timeText}`,
            });
          }

          if (lastMessage.type === 'grace_period_adjusted' && lastMessage.data?.userId === (currentUser as any)?.id) {
            const gracePeriodMinutes = lastMessage.data.newGracePeriodMinutes;

            if (gracePeriodMinutes > 0) {
              const hours = Math.floor(gracePeriodMinutes / 60);
              const minutes = gracePeriodMinutes % 60;
              const timeText = hours > 0 ? `${hours}h ${minutes}m` : `${minutes}m`;

              toast({
                title: "Grace Period Extended",
                description: `Your locker grace period has been set to ${timeText}`,
              });
            } else {
              toast({
                title: "Grace Period Ended",
                description: "Your locker grace period has been removed",
                variant: "destructive",
              });
            }
          }
        }
      } catch (error) {
        console.error("Error parsing WebSocket message:", error);
      }
    }
  }, [lastMessage, currentUser, toast]);

  // Initialize mobile features
  useEffect(() => {
    const savedUser = localStorage.getItem("user_data");
    if (savedUser) {
      try {
        const userData = JSON.parse(savedUser);
        console.log("Restored user from localStorage:", userData);

        // Clear restriction modal state when restoring user
        setShowRestrictionModal(false);
        setRestrictionReason("");

        setCurrentUser(userData);
        setCurrentScreen("dashboard");
      } catch (error) {
        console.error("Error parsing saved user data:", error);
        localStorage.removeItem("user_data");
      }
    }

    // Initialize mobile features
    const initMobileFeatures = async () => {
      try {
        await mobileFeatures.requestPermissions();
        await mobileFeatures.setupPushNotifications();

        // Set status bar style for user app
        await mobileFeatures.setStatusBarStyle('dark');
        await mobileFeatures.setStatusBarColor('#2563eb');
      } catch (error) {
        console.error('Mobile features initialization failed:', error);
      }
    };

    initMobileFeatures();
  }, []);



  // Early return for debugging - ensure component always renders something
  if (!currentScreen) {
    return (
      <div className="min-h-screen bg-red-500 text-white flex items-center justify-center">
        <div>Error: No current screen set</div>
      </div>
    );
  }

  // Generate real notifications based on user's actual data
  const generateRealNotifications = () => {
    const realNotifications = [];

    // Add notifications for recent bookings
    if (userBookings && userBookings.length > 0) {
      userBookings.forEach((booking: any) => {
        // Booking confirmation notification
        realNotifications.push({
          id: `booking-${booking.id}`,
          title: "Locker Reserved",
          message: `Your locker ${booking.locker?.code || `#${booking.lockerId}`} has been successfully reserved`,
          type: "booking_confirmed" as const,
          isRead: false,
          timestamp: booking.createdAt,
          bookingId: booking.id,
          agentName: undefined
        });

        // Payment confirmation notification
        if (booking.paymentStatus === 'paid' || booking.paymentStatus === 'simulated_paid') {
          realNotifications.push({
            id: `payment-${booking.id}`,
            title: "Payment Confirmed",
            message: `Payment of $${booking.totalCost?.toFixed(2) || '0.00'} has been processed for your locker rental`,
            type: "booking_confirmed" as const,
            isRead: false,
            timestamp: booking.createdAt + 1000, // Slightly after booking
            bookingId: booking.id,
            agentName: undefined
          });
        }

        // Agent assignment notification
        if (booking.agentId || booking.agent) {
          const agentName = booking.agent?.firstName && booking.agent?.lastName
            ? `${booking.agent.firstName} ${booking.agent.lastName}`
            : booking.agent?.username || 'Agent';

          realNotifications.push({
            id: `agent-${booking.id}`,
            title: "Agent Assigned",
            message: `${agentName} has been assigned to help with your delivery`,
            type: "agent_access" as const,
            isRead: false,
            timestamp: booking.createdAt + 2000, // After payment
            bookingId: booking.id,
            agentName
          });
        }

        // Booking status notifications
        if (booking.status === 'active') {
          const now = Date.now();
          const endTime = booking.endTime;
          const timeRemaining = endTime - now;

          // Add expiration warning if booking is ending soon
          if (timeRemaining > 0 && timeRemaining <= 15 * 60 * 1000) { // 15 minutes or less
            realNotifications.push({
              id: `warning-${booking.id}`,
              title: "Locker Expiring Soon",
              message: `Your locker ${booking.locker?.code || `#${booking.lockerId}`} will expire in ${Math.ceil(timeRemaining / (60 * 1000))} minutes`,
              type: "locker_expiration" as const,
              isRead: false,
              timestamp: now - (15 * 60 * 1000 - timeRemaining), // When the warning should have been sent
              bookingId: booking.id,
              agentName: undefined
            });
          }
        }
      });
    }

    // Add notifications for booking history
    if (userBookingHistory && userBookingHistory.length > 0) {
      userBookingHistory.slice(0, 3).forEach((booking: any) => {
        const lockerCode = booking.locker?.code || `#${booking.lockerId}`;
        const duration = booking.duration || Math.ceil((booking.endTime - booking.startTime) / (60 * 1000));

        if (booking.status === 'completed') {
          realNotifications.push({
            id: `completed-${booking.id}`,
            title: "Booking Completed",
            message: `Your ${duration}-minute rental of locker ${lockerCode} has been completed successfully`,
            type: "task_completed" as const,
            isRead: true,
            timestamp: booking.endTime || booking.updatedAt,
            bookingId: booking.id,
            agentName: undefined
          });
        } else if (booking.status === 'expired') {
          realNotifications.push({
            id: `expired-${booking.id}`,
            title: "Booking Expired",
            message: `Your rental of locker ${lockerCode} has expired. Please contact support if you need assistance.`,
            type: "locker_expiration" as const,
            isRead: true,
            timestamp: booking.endTime || booking.updatedAt,
            bookingId: booking.id,
            agentName: undefined
          });
        } else if (booking.status === 'cancelled') {
          realNotifications.push({
            id: `cancelled-${booking.id}`,
            title: "Booking Cancelled",
            message: `Your booking for locker ${lockerCode} has been cancelled`,
            type: "general" as const,
            isRead: true,
            timestamp: booking.updatedAt,
            bookingId: booking.id,
            agentName: undefined
          });
        }
      });
    }

    // Sort by timestamp (newest first) and limit to recent notifications
    return realNotifications
      .sort((a, b) => b.timestamp - a.timestamp)
      .slice(0, 10); // Keep only the 10 most recent
  };

  const displayNotifications = notifications.length > 0 ? notifications : generateRealNotifications();
  const displayUnreadCount = notifications.length > 0 ? unreadCount : displayNotifications.filter(n => !n.isRead).length;

  const handleLogin = async (username: string, password: string) => {
    try {
      const response = await fetch("/api/auth/login", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ username, password, role: "user" }),
      });

      if (!response.ok) {
        let errorMessage = "Login failed";
        try {
          const errorData = await response.json();

          // Handle restriction/suspension specifically
          if (errorData.status === "restricted" || errorData.status === "suspended") {
            // Reset modal state first, then set it with the restriction details
            setShowRestrictionModal(false);
            setRestrictionReason("");

            setTimeout(() => {
              setRestrictionReason(errorData.reason || errorData.message);
              setShowRestrictionModal(true);
            }, 100);

            return; // Don't throw error, just show restriction modal
          }

          errorMessage = errorData.message || errorMessage;
        } catch (e) {
          console.error("Error parsing error response:", e);
        }
        throw new Error(errorMessage);
      }

      const userData = await response.json();

      setCurrentUser(userData);
      localStorage.setItem("user_data", JSON.stringify(userData));
      setCurrentScreen("dashboard");

      toast({
        title: "Welcome back!",
        description: `Logged in as ${userData.username || userData.email}`,
      });
    } catch (error) {
      toast({
        title: "Login Failed",
        description: error instanceof Error ? error.message : "Please check your credentials",
        variant: "destructive",
      });
    }
  };

  const handleLogout = () => {
    setCurrentUser(null);
    setSelectedLocker(null);
    localStorage.removeItem("user_data");
    setCurrentScreen("login");

    toast({
      title: "Logged out",
      description: "You have been successfully logged out",
    });
  };

  // Handle date/time selection
  const handleDateTimeSelect = (startDate: Date, endDate: Date, duration: number) => {
    setSelectedDateTime({ startDate, endDate, duration });
    setShowDateTimePicker(false);
    setCurrentScreen("booking");
  };

  const handleDateTimeCancel = () => {
    setShowDateTimePicker(false);
  };

  // Function to create real-time notifications
  const createNotification = (notification: Omit<Notification, 'id' | 'isRead'>) => {
    const newNotification = {
      ...notification,
      id: `${notification.type}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      isRead: false,
    };

    // Add to notifications using the hook
    // This will be handled by the useNotifications hook when WebSocket messages arrive
    return newNotification;
  };

  // Handle QR code scan
  const handleQRScan = async (qrData: string) => {
    try {
      await mobileFeatures.vibrate('medium');

      // Parse QR code data (format: LOCKER:A001:UNLOCK or BOOKING:12345:CONFIRM)
      const parts = qrData.split(':');

      if (parts[0] === 'LOCKER' && parts[2] === 'UNLOCK') {
        const lockerCode = parts[1];
        toast({
          title: "QR Code Scanned",
          description: `Attempting to unlock locker ${lockerCode}`,
        });

        // Here you would send unlock command to the locker
        // For demo, just show success
        setTimeout(() => {
          toast({
            title: "Locker Unlocked",
            description: `Locker ${lockerCode} has been unlocked successfully`,
          });
        }, 1500);

      } else if (parts[0] === 'BOOKING' && parts[2] === 'CONFIRM') {
        const bookingId = parts[1];
        toast({
          title: "Booking Confirmed",
          description: `Booking #${bookingId} has been confirmed`,
        });
      } else {
        toast({
          title: "Invalid QR Code",
          description: "This QR code is not recognized by the system",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('QR scan error:', error);
      toast({
        title: "Scan Failed",
        description: "Failed to process QR code",
        variant: "destructive",
      });
    }
  };

  // Handle location update
  const handleLocationUpdate = (location: { lat: number; lng: number }) => {
    setUserLocation(location);
    console.log('User location updated:', location);
  };

  // Premium feature handlers
  const handlePremiumUpgrade = (planName: string, billingCycle: 'monthly' | 'yearly') => {
    // Here you would integrate with your payment processor (Stripe, PayPal, etc.)
    console.log('Upgrading to:', planName, billingCycle);

    // For demo purposes, simulate successful upgrade
    const updatedUser = {
      ...currentUser,
      subscriptionType: planName,
      subscriptionStatus: 'active',
      subscriptionStartDate: Date.now(),
      subscriptionEndDate: Date.now() + (billingCycle === 'yearly' ? 365 : 30) * 24 * 60 * 60 * 1000,
      trialEndDate: Date.now() + 7 * 24 * 60 * 60 * 1000 // 7-day trial
    };

    setCurrentUser(updatedUser);
    localStorage.setItem("user_data", JSON.stringify(updatedUser));

    toast({
      title: "Upgrade Successful!",
      description: `Welcome to ${planName}! Your 7-day free trial has started.`,
    });

    return Promise.resolve();
  };

  const checkPremiumAccess = (feature: string): boolean => {
    if (!currentUser) return false;

    const canAccess = subscriptionService.canAccessPremiumFeature(currentUser, feature);

    if (!canAccess) {
      setPremiumTriggerFeature(feature);
      setShowPremiumUpgrade(true);
    }

    return canAccess;
  };

  const handlePremiumFeatureAttempt = (feature: string, action: () => void) => {
    if (checkPremiumAccess(feature)) {
      action();
    }
  };

  // Handle locker reservation with premium checks
  const handleReserveLocker = async (lockerId: number, duration: number = 60) => {
    // Check booking limits
    const limits = subscriptionService.getFeatureLimits(currentUser);
    const currentBookings = mockBookings.filter(b => b.userId === currentUser?.id && b.status === 'active').length;

    // Check monthly booking limit
    if (limits.maxBookingsPerMonth !== -1) {
      const currentMonth = new Date().getMonth();
      const monthlyBookings = mockBookings.filter(b =>
        b.userId === currentUser?.id &&
        new Date(b.startTime).getMonth() === currentMonth
      ).length;

      if (monthlyBookings >= limits.maxBookingsPerMonth) {
        setPremiumTriggerFeature('unlimited_bookings');
        setShowPremiumUpgrade(true);
        return;
      }
    }

    // Check concurrent booking limit
    if (currentBookings >= limits.maxConcurrentBookings) {
      toast({
        title: "Booking Limit Reached",
        description: `You can only have ${limits.maxConcurrentBookings} active booking(s) at a time.`,
        variant: "destructive",
      });
      return;
    }

    // Check if it's a premium locker
    const locker = mockLockers.find(l => l.id === lockerId);
    if (locker?.isPremium && !limits.canAccessPremiumLockers) {
      setPremiumTriggerFeature('premium_lockers');
      setShowPremiumUpgrade(true);
      return;
    }
    if (!currentUser) {
      toast({
        title: "Authentication Required",
        description: "Please log in to reserve a locker",
        variant: "destructive",
      });
      return;
    }

    try {
      const response = await fetch("/api/bookings", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          userId: (currentUser as any).id,
          lockerId,
          duration: duration * 60, // Convert hours to minutes for server
          startTime: Date.now(),
          endTime: Date.now() + (duration * 60 * 60 * 1000), // Convert hours to milliseconds
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to reserve locker");
      }

      const booking = await response.json();

      // Find the locker details
      const locker = lockers.find(l => l.id === lockerId);
      const lockerCode = locker?.code || `#${lockerId}`;

      toast({
        title: "Locker Reserved!",
        description: `Locker ${lockerCode} has been reserved for ${duration} minutes`,
      });

      // Create real-time notifications for the booking
      createNotification({
        type: 'booking_confirmed',
        title: 'Locker Reserved',
        message: `Your locker ${lockerCode} has been successfully reserved`,
        timestamp: Date.now(),
        userId: (currentUser as any).id,
        bookingId: booking.id
      });

      // Create payment notification
      createNotification({
        type: 'payment',
        title: 'Payment Confirmed',
        message: `Payment of $${booking.totalCost?.toFixed(2) || '0.00'} has been processed for your locker rental`,
        timestamp: Date.now() + 1000,
        userId: (currentUser as any).id,
        bookingId: booking.id
      });

      // Refresh data
      queryClient.invalidateQueries({ queryKey: ["/api/bookings"] });
      queryClient.invalidateQueries({ queryKey: ["/api/bookings/user"] });
      queryClient.invalidateQueries({ queryKey: ["/api/bookings/user/history"] });
      queryClient.invalidateQueries({ queryKey: ["/api/lockers"] });
      queryClient.invalidateQueries({ queryKey: ["/api/stats"] });

      setCurrentScreen("my-lockers");
    } catch (error) {
      console.error("Reservation error:", error);
      toast({
        title: "Reservation Failed",
        description: error instanceof Error ? error.message : "Failed to reserve locker",
        variant: "destructive",
      });
    }
  };

  const renderBottomNav = () => (
    <div className="absolute bottom-0 left-0 right-0 bg-white border-t p-4 z-30">
      <div className="grid grid-cols-4 gap-4 max-w-md mx-auto">
        <button
          onClick={() => setCurrentScreen("dashboard")}
          className={`flex flex-col items-center space-y-1 ${currentScreen === "dashboard" ? "text-orange-600" : "text-gray-400"}`}
        >
          <Home className="w-5 h-5" />
          <span className="text-xs">Home</span>
        </button>
        <button
          onClick={() => setCurrentScreen("map")}
          className={`flex flex-col items-center space-y-1 ${currentScreen === "map" ? "text-orange-600" : "text-gray-400"}`}
        >
          <MapPin className="w-5 h-5" />
          <span className="text-xs">Locate</span>
        </button>
        <button
          onClick={() => setCurrentScreen("scan")}
          className={`flex flex-col items-center space-y-1 ${currentScreen === "scan" ? "text-orange-600" : "text-gray-400"}`}
        >
          <QrCode className="w-5 h-5" />
          <span className="text-xs">Scan</span>
        </button>
        <button
          onClick={() => setCurrentScreen("profile")}
          className={`flex flex-col items-center space-y-1 ${currentScreen === "profile" ? "text-orange-600" : "text-gray-400"}`}
        >
          <User className="w-5 h-5" />
          <span className="text-xs">Profile</span>
        </button>
      </div>
    </div>
  );

  if (currentScreen === "login") {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center p-4">
        {/* Phone Frame */}
        <div className="relative">
          {/* Phone Outer Frame */}
          <div className="w-[375px] h-[812px] bg-black rounded-[3rem] p-2 shadow-2xl">
            {/* Phone Screen */}
            <div className="w-full h-full bg-white rounded-[2.5rem] overflow-hidden relative">
              {/* Status Bar */}
              <div className="absolute top-0 left-0 right-0 h-11 bg-black rounded-t-[2.5rem] flex items-center justify-between px-6 text-white text-sm font-medium z-10">
                <span>9:41</span>
                <div className="flex items-center space-x-1">
                  <div className="flex space-x-1">
                    <div className="w-1 h-1 bg-white rounded-full"></div>
                    <div className="w-1 h-1 bg-white rounded-full"></div>
                    <div className="w-1 h-1 bg-white rounded-full"></div>
                    <div className="w-1 h-1 bg-white rounded-full"></div>
                  </div>
                  <svg className="w-6 h-3" viewBox="0 0 24 12" fill="white">
                    <rect x="1" y="3" width="18" height="6" rx="2" stroke="white" strokeWidth="1" fill="none"/>
                    <rect x="20" y="5" width="2" height="2" rx="0.5" fill="white"/>
                  </svg>
                </div>
              </div>

              {/* App Content */}
              <div className="pt-11 h-full bg-gradient-to-br from-blue-50 to-indigo-100 px-4 overflow-y-auto flex flex-col justify-center">
            <div className="text-center mb-8 mt-8">
              <div className="w-20 h-20 bg-blue-600 rounded-xl mx-auto mb-4 flex items-center justify-center">
                <Package className="w-10 h-10 text-white" />
              </div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">SwiSto</h1>
              <p className="text-gray-600">Smart Locker System</p>
            </div>

            <Card className="shadow-lg">
              <CardHeader>
                <CardTitle>Welcome Back</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="username">Username</Label>
                  <Input
                    id="username"
                    type="text"
                    placeholder="Enter your username"
                    value={userCredentials.username}
                    onChange={(e) => setUserCredentials({...userCredentials, username: e.target.value})}
                    className="h-12"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="password">Password</Label>
                  <Input
                    id="password"
                    type="password"
                    placeholder="Enter your password"
                    value={userCredentials.password}
                    onChange={(e) => setUserCredentials({...userCredentials, password: e.target.value})}
                    className="h-12"
                  />
                </div>
                <Button
                  onClick={() => handleLogin(userCredentials.username, userCredentials.password)}
                  className="w-full h-12 text-lg"
                  disabled={!userCredentials.username || !userCredentials.password}
                >
                  Sign In
                </Button>
                <div className="text-center space-y-2">
                  <Button
                    variant="ghost"
                    onClick={() => setCurrentScreen("register")}
                    className="text-sm w-full"
                  >
                    <UserPlus className="w-4 h-4 mr-2" />
                    Create Account
                  </Button>
                  <Button
                    variant="ghost"
                    onClick={() => setShowSupportChat(true)}
                    className="text-sm w-full text-blue-600 hover:text-blue-700"
                  >
                    <MessageCircle className="w-4 h-4 mr-2" />
                    Contact Support
                  </Button>
                </div>
              </CardContent>
            </Card>
              </div>
            </div>
          </div>
        </div>

        {/* Support Chat Modal for Login Screen */}
        {showSupportChat && (
          <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
            <div className="bg-white rounded-lg w-full max-w-md h-[600px] flex flex-col">
              <SupportChat
                currentUser={{
                  id: 'guest-user',
                  username: 'Guest User',
                  role: 'user',
                  email: '<EMAIL>'
                }}
                onClose={() => setShowSupportChat(false)}
              />
            </div>
          </div>
        )}

        {/* Restriction Modal for Login Screen */}
        <RestrictionModal
          isOpen={showRestrictionModal}
          reason={restrictionReason}
          onContactSupport={() => {
            setShowRestrictionModal(false);
            setShowSupportModal(true);
          }}
          onClose={() => setShowRestrictionModal(false)}
        />

        {/* Support Modal for Login Screen */}
        <SupportModal
          isOpen={showSupportModal}
          onClose={() => setShowSupportModal(false)}
          userType="user"
        />
      </div>
    );
  }

  if (currentScreen === "register") {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center p-4">
        {/* Phone Frame */}
        <div className="relative">
          {/* Phone Outer Frame */}
          <div className="w-[375px] h-[812px] bg-black rounded-[3rem] p-2 shadow-2xl">
            {/* Phone Screen */}
            <div className="w-full h-full bg-white rounded-[2.5rem] overflow-hidden relative">
              {/* Status Bar */}
              <div className="absolute top-0 left-0 right-0 h-11 bg-black rounded-t-[2.5rem] flex items-center justify-between px-6 text-white text-sm font-medium z-10">
                <span>9:41</span>
                <div className="flex items-center space-x-1">
                  <div className="flex space-x-1">
                    <div className="w-1 h-1 bg-white rounded-full"></div>
                    <div className="w-1 h-1 bg-white rounded-full"></div>
                    <div className="w-1 h-1 bg-white rounded-full"></div>
                    <div className="w-1 h-1 bg-white rounded-full"></div>
                  </div>
                  <svg className="w-6 h-3" viewBox="0 0 24 12" fill="white">
                    <rect x="1" y="3" width="18" height="6" rx="2" stroke="white" strokeWidth="1" fill="none"/>
                    <rect x="20" y="5" width="2" height="2" rx="0.5" fill="white"/>
                  </svg>
                </div>
              </div>

              {/* App Content */}
              <div className="pt-11 h-full bg-gradient-to-br from-blue-50 to-indigo-100 flex flex-col overflow-y-auto">
            <div className="flex-shrink-0 px-4 pt-6 pb-4">
              <Button
                variant="ghost"
                onClick={() => setCurrentScreen("login")}
                className="mb-4 hover:bg-white/50 rounded-lg"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Login
              </Button>
            </div>
            <div className="flex-1 overflow-y-auto px-2 pb-6">
              <RegistrationForm
                onSuccess={() => {
                  setCurrentScreen("login");
                  toast({
                    title: "Account Created!",
                    description: "Please log in with your new credentials.",
                  });
                }}
                onCancel={() => setCurrentScreen("login")}
              />
            </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center p-4">
      {/* Phone Frame */}
      <div className="relative">
        {/* Phone Outer Frame */}
        <div className="w-[375px] h-[812px] bg-black rounded-[3rem] p-2 shadow-2xl">
          {/* Phone Screen */}
          <div className="w-full h-full bg-white rounded-[2.5rem] overflow-hidden relative">
            {/* Status Bar */}
            <div className="absolute top-0 left-0 right-0 h-11 bg-black rounded-t-[2.5rem] flex items-center justify-between px-6 text-white text-sm font-medium z-10">
              <span>9:41</span>
              <div className="flex items-center space-x-1">
                <div className="flex space-x-1">
                  <div className="w-1 h-1 bg-white rounded-full"></div>
                  <div className="w-1 h-1 bg-white rounded-full"></div>
                  <div className="w-1 h-1 bg-white rounded-full"></div>
                  <div className="w-1 h-1 bg-white rounded-full"></div>
                </div>
                <svg className="w-6 h-3" viewBox="0 0 24 12" fill="white">
                  <rect x="1" y="3" width="18" height="6" rx="2" stroke="white" strokeWidth="1" fill="none"/>
                  <rect x="20" y="5" width="2" height="2" rx="0.5" fill="white"/>
                </svg>
              </div>
            </div>

            {/* App Content */}
            <div className="pt-11 h-full bg-gray-50 relative flex flex-col overflow-y-auto">
              {/* Support Chat Overlay */}
              {showSupportChat && (
                <SupportChat
                  currentUser={currentUser}
                  onClose={() => setShowSupportChat(false)}
                />
              )}

      {currentScreen === "dashboard" && (
        <>
          {/* Header */}
          <div className="bg-white px-4 py-4 border-b border-gray-100">
            <div className="flex items-center justify-between mb-6">
              <h1 className="text-2xl font-bold text-gray-900">SwiSto</h1>
              <div className="flex items-center space-x-4">
                <button
                  onClick={() => setShowNotifications(true)}
                  className="relative p-2 hover:bg-gray-100 rounded-full transition-colors"
                >
                  <Bell className="w-6 h-6 text-gray-600" />
                  {displayUnreadCount > 0 && (
                    <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                      {displayUnreadCount > 9 ? '9+' : displayUnreadCount}
                    </span>
                  )}
                </button>
                <button
                  onClick={() => setCurrentScreen("profile")}
                  className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center hover:from-blue-600 hover:to-purple-700 transition-all"
                >
                  <User className="w-5 h-5 text-white" />
                </button>
              </div>
            </div>

            <div className="mb-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">
                Welcome back, {(currentUser as any)?.username || 'Alex'}
              </h2>

              <Button
                className="w-full bg-black hover:bg-gray-800 text-white py-4 text-lg font-medium rounded-xl mb-6 shadow-lg active:scale-95 transition-transform"
                onClick={() => setCurrentScreen("selection")}
              >
                Reserve a Locker
              </Button>

              <div className="grid grid-cols-2 gap-6 text-left">
                <div className="bg-gray-50 p-4 rounded-xl">
                  <p className="text-gray-500 text-sm mb-1">Available Lockers</p>
                  <p className="text-3xl font-bold text-gray-900">
                    {(stats as any)?.availableLockers ||
                     (Array.isArray(lockers) ? lockers.filter((locker: any) => locker.status === 'available').length : 0)}
                  </p>
                </div>
                <div className="bg-gray-50 p-4 rounded-xl">
                  <p className="text-gray-500 text-sm mb-1">Active Bookings</p>
                  <p className="text-3xl font-bold text-gray-900">
                    {Array.isArray(userBookings) ? userBookings.length : 0}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 p-4 pb-24 overflow-y-auto">
            <div className="mb-6">
              <LockerMap />
            </div>

            {/* Camera Preview */}
            <Card className="mb-6 rounded-xl shadow-sm">
              <CardContent className="p-4">
                <div className="bg-gray-800 h-32 rounded-lg flex items-center justify-center mb-3">
                  <Camera className="w-8 h-8 text-white" />
                </div>
                <p className="text-xs text-gray-500 text-center">Camera preview for item verification</p>
              </CardContent>
            </Card>

            <div className="grid grid-cols-2 gap-4 mb-4">
              <Button
                variant="outline"
                className="h-24 flex flex-col justify-center items-center space-y-2 border-gray-200 rounded-xl hover:bg-gray-50 active:scale-95 transition-all"
                onClick={() => setCurrentScreen("my-lockers")}
              >
                <Lock className="w-7 h-7 text-gray-600" />
                <span className="text-sm font-medium text-gray-900">My Lockers</span>
              </Button>
              <Button
                variant="outline"
                className="h-24 flex flex-col justify-center items-center space-y-2 border-gray-200 rounded-xl hover:bg-gray-50 active:scale-95 transition-all"
                onClick={() => setCurrentScreen("history")}
              >
                <History className="w-7 h-7 text-gray-600" />
                <span className="text-sm font-medium text-gray-900">History</span>
              </Button>
            </div>

            {/* Agent Services Button */}
            <Button
              variant="outline"
              onClick={() => {
                // If user has active bookings, show agent coordination for the first one
                if (userBookings && userBookings.length > 0) {
                  setSelectedBookingForAgent(userBookings[0]);
                  setShowAgentCoordination(true);
                } else {
                  toast({
                    title: "No Active Bookings",
                    description: "You need an active locker booking to coordinate with an agent",
                    variant: "destructive",
                  });
                }
              }}
              className="w-full py-3 mb-4 rounded-xl border-2 border-blue-200 hover:bg-blue-50 hover:border-blue-300 hover:text-blue-600 transition-colors flex items-center justify-center space-x-2"
            >
              <User className="w-5 h-5" />
              <span className="font-medium">Agent Services</span>
            </Button>

            {/* Contact Support Button */}
            <Button
              variant="outline"
              onClick={() => setShowSupportChat(true)}
              className="w-full py-3 rounded-xl border-2 border-blue-200 hover:bg-blue-50 hover:border-blue-300 hover:text-blue-600 transition-colors flex items-center justify-center space-x-2"
            >
              <MessageCircle className="w-5 h-5" />
              <span className="font-medium">Contact Support</span>
            </Button>
          </div>
          {renderBottomNav()}
        </>
      )}

      {currentScreen === "selection" && (
        <>
          <div className="bg-white px-4 py-4 border-b border-gray-100 flex items-center">
            <button
              onClick={() => setCurrentScreen("dashboard")}
              className="mr-4 p-2 hover:bg-gray-100 rounded-full transition-colors"
            >
              <ArrowLeft className="w-5 h-5" />
            </button>
            <h1 className="text-lg font-semibold">Select Locker Type</h1>
          </div>
          <div className="flex-1 pb-24 overflow-y-auto">
            <LockerSelection
              currentUser={currentUser}
              onContinue={(data) => {
                setSelectedLockerData(data);
                setShowDateTimePicker(true);
              }}
            />
          </div>
          {renderBottomNav()}
        </>
      )}

      {currentScreen === "booking" && (
        <>
          <div className="bg-white px-4 py-4 border-b border-gray-100 flex items-center">
            <button
              onClick={() => setCurrentScreen("selection")}
              className="mr-4 p-2 hover:bg-gray-100 rounded-full transition-colors"
            >
              <ArrowLeft className="w-5 h-5" />
            </button>
            <h1 className="text-lg font-semibold">Booking Details</h1>
          </div>
          <div className="flex-1 pb-24 overflow-y-auto">
            <BookingFlow
              selectedLocker={selectedLockerData}
              selectedDateTime={selectedDateTime}
              currentUser={currentUser}
              onComplete={() => {
                setSelectedLockerData(null);
                setSelectedDateTime(null);
                setCurrentScreen("dashboard");
              }}
            />
          </div>
          {renderBottomNav()}
        </>
      )}

      {currentScreen === "map" && (
        <>
          <div className="bg-white px-4 py-4 border-b border-gray-100 flex items-center">
            <button
              onClick={() => setCurrentScreen("dashboard")}
              className="mr-4 p-2 hover:bg-gray-100 rounded-full transition-colors"
            >
              <ArrowLeft className="w-5 h-5" />
            </button>
            <h1 className="text-lg font-semibold">Locker Locations</h1>
          </div>
          <div className="flex-1 p-4 pb-24 overflow-y-auto">
            <div className="space-y-6">
              {/* Location Tracker */}
              <LocationTracker
                onLocationUpdate={handleLocationUpdate}
                showMap={true}
                autoStart={false}
              />

              {/* Interactive Mobile Map */}
              <Card className="rounded-xl shadow-sm overflow-hidden">
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <MapPin className="w-5 h-5" />
                      <span>Interactive Map</span>
                    </div>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => setCurrentScreen("fullscreen-map")}
                      className="text-xs"
                    >
                      Fullscreen
                    </Button>
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-0">
                  <div className="h-80">
                    <MobileMap
                      userLocation={userLocation}
                      height="h-80"
                      showUserLocation={true}
                      showLockers={true}
                      interactive={true}
                      onLocationSelect={(location) => {
                        toast({
                          title: "Location Selected",
                          description: `${location.name} - ${location.availableLockers} lockers available`,
                        });
                        // Could navigate to booking flow here
                        // setCurrentScreen("selection");
                      }}
                    />
                  </div>
                </CardContent>
              </Card>

              {/* Location Details */}
              <Card className="rounded-xl shadow-sm">
                <CardHeader>
                  <CardTitle>Location Details</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="font-medium">Main Building</span>
                    <div className="flex items-center space-x-1">
                      <Star className="w-4 h-4 text-yellow-500" />
                      <span className="text-sm">4.8</span>
                    </div>
                  </div>
                  <p className="text-sm text-gray-600">Ground floor, near main entrance</p>
                  <div className="flex items-center space-x-4 text-sm">
                    <span className="text-green-600">24/7 Access</span>
                    <span className="text-blue-600">Climate Controlled</span>
                  </div>

                  {userLocation && (
                    <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                      <p className="text-sm text-blue-800 font-medium">Your Location</p>
                      <p className="text-xs text-blue-600">
                        {userLocation.lat.toFixed(6)}, {userLocation.lng.toFixed(6)}
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
          {renderBottomNav()}
        </>
      )}

      {currentScreen === "fullscreen-map" && (
        <>
          <div className="bg-white px-4 py-4 border-b border-gray-100 flex items-center">
            <button
              onClick={() => setCurrentScreen("map")}
              className="mr-4 p-2 hover:bg-gray-100 rounded-full transition-colors"
            >
              <ArrowLeft className="w-5 h-5" />
            </button>
            <h1 className="text-lg font-semibold">Interactive Map</h1>
            <div className="ml-auto flex items-center space-x-2">
              {userLocation && (
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => {
                    // Center map on user location
                    toast({
                      title: "Centered on Your Location",
                      description: "Map centered on your current position",
                    });
                  }}
                  className="text-xs"
                >
                  <Navigation className="w-4 h-4 mr-1" />
                  Center
                </Button>
              )}
            </div>
          </div>
          <div className="flex-1 relative">
            <MobileMap
              userLocation={userLocation}
              height="h-full"
              showUserLocation={true}
              showLockers={true}
              interactive={true}
              onLocationSelect={(location) => {
                toast({
                  title: "Location Selected",
                  description: `${location.name} - ${location.availableLockers} lockers available`,
                  action: {
                    label: "Book Now",
                    onClick: () => {
                      setCurrentScreen("selection");
                    }
                  }
                });
              }}
            />
          </div>
          {renderBottomNav()}
        </>
      )}

      {currentScreen === "biometric" && !selectedLocker && (
        <>
          <div className="p-4 border-b bg-white flex items-center">
            <button onClick={() => setCurrentScreen("dashboard")} className="mr-4">
              <ArrowLeft className="w-5 h-5" />
            </button>
            <h1 className="text-lg font-semibold">Select Locker</h1>
          </div>
          <div className="p-4 pb-20">
            <LockerSelector
              onSelectLocker={(lockerId, lockerCode) => {
                setSelectedLocker({ id: lockerId, code: lockerCode });
              }}
              onCancel={() => setCurrentScreen("dashboard")}
            />
          </div>
          {renderBottomNav()}
        </>
      )}

      {currentScreen === "biometric" && selectedLocker && (
        <>
          <div className="p-4 border-b bg-white flex items-center">
            <button onClick={() => {
              setSelectedLocker(null);
            }} className="mr-4">
              <ArrowLeft className="w-5 h-5" />
            </button>
            <h1 className="text-lg font-semibold">Unlock Locker {selectedLocker.code}</h1>
          </div>
          <div className="p-4 pb-20">
            <BiometricUnlock
              lockerId={selectedLocker.id}
              lockerCode={selectedLocker.code}
              bookingId={selectedLocker.bookingId}
              onSuccess={() => {
                setSelectedLocker(null);
                setCurrentScreen("dashboard");
              }}
              onCancel={() => {
                setSelectedLocker(null);
                setCurrentScreen("dashboard");
              }}
            />
          </div>
          {renderBottomNav()}
        </>
      )}

      {currentScreen === "my-lockers" && (
        <>
          <div className="bg-white px-4 py-4 border-b border-gray-100 flex items-center">
            <button
              onClick={() => setCurrentScreen("dashboard")}
              className="mr-4 p-2 hover:bg-gray-100 rounded-full transition-colors active:scale-95 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
              aria-label="Back to dashboard"
            >
              <ArrowLeft className="w-5 h-5" />
            </button>
            <h1 className="text-lg font-semibold">My Lockers</h1>
          </div>
          <div className="flex-1 p-4 pb-24 overflow-y-auto">
            {Array.isArray(userBookings) && userBookings.length > 0 ? (
              <div className="space-y-4">
                {userBookings.map((booking: any) => {
                  // Check if booking is expired, considering grace period
                  const now = Date.now();
                  const isBookingExpired = booking.endTime && now > booking.endTime;
                  const isActive = booking.status === 'active' || booking.status === 'confirmed';

                  // Check if there's an active grace period
                  const hasActiveGracePeriod = booking.accessSession &&
                    booking.accessSession.status === 'grace_period' &&
                    booking.accessSession.graceEnd &&
                    now < booking.accessSession.graceEnd;

                  // Debug logging (can be removed in production)
                  if (process.env.NODE_ENV === 'development') {
                    console.log(`Booking ${booking.id} debug:`, {
                      bookingId: booking.id,
                      isBookingExpired,
                      hasAccessSession: !!booking.accessSession,
                      accessSessionStatus: booking.accessSession?.status,
                      graceEnd: booking.accessSession?.graceEnd,
                      now,
                      hasActiveGracePeriod,
                      timeUntilGraceEnd: booking.accessSession?.graceEnd ? booking.accessSession.graceEnd - now : null
                    });
                  }

                  // Locker is considered "expired and locked" only if:
                  // 1. Booking time has ended AND
                  // 2. There's no active grace period
                  const isExpired = isBookingExpired && !hasActiveGracePeriod;

                  return (
                    <Card key={booking.id} className="rounded-xl shadow-sm">
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${
                              hasActiveGracePeriod ? 'bg-orange-100' :
                              isExpired && isActive ? 'bg-red-100' : 'bg-blue-100'
                            }`}>
                              <Lock className={`w-6 h-6 ${
                                hasActiveGracePeriod ? 'text-orange-600' :
                                isExpired && isActive ? 'text-red-600' : 'text-blue-600'
                              }`} />
                            </div>
                            <div>
                              <h4 className="font-medium text-gray-900">
                                Locker {booking.locker?.code || booking.lockerId}
                              </h4>
                              <p className="text-sm text-gray-500">
                                {booking.locker?.location?.name || 'Main Building'}
                              </p>
                              <p className="text-xs text-gray-400">
                                Status: {
                                  hasActiveGracePeriod ? 'Grace Period' :
                                  isExpired && isActive ? 'Expired' :
                                  booking.status
                                }
                              </p>
                              {hasActiveGracePeriod && (
                                <p className="text-xs text-orange-500 font-medium">
                                  Grace period until {new Date(booking.accessSession.graceEnd).toLocaleString()}
                                </p>
                              )}
                              {isExpired && isActive && !hasActiveGracePeriod && (
                                <p className="text-xs text-red-500 font-medium">
                                  Expired on {new Date(booking.endTime).toLocaleDateString()}
                                </p>
                              )}
                            </div>
                          </div>
                          <div className="text-right">
                            <p className="text-sm font-medium text-gray-900">
                              ${booking.totalCost || '5.00'}
                            </p>
                            <p className="text-xs text-gray-500">
                              {new Date(booking.startTime).toLocaleDateString()}
                            </p>
                          </div>
                        </div>
                        <div className="mt-3 flex flex-wrap gap-2 overflow-x-auto scrollbar-hide">
                          {isExpired && isActive && !hasActiveGracePeriod ? (
                            <Button
                              size="sm"
                              onClick={() => {
                                console.log('Expired locker clicked:', booking);
                                setShowExpiredLockerDialog(booking);
                              }}
                              className="bg-red-600 hover:bg-red-700 text-white rounded-lg flex-1"
                            >
                              <Lock className="w-4 h-4 mr-2" />
                              Locked
                            </Button>
                          ) : (
                            <Button
                              size="sm"
                              onClick={() => {
                                setSelectedLocker({
                                  id: booking.lockerId,
                                  code: booking.locker?.code || booking.lockerId,
                                  bookingId: booking.id
                                });
                                setCurrentScreen("biometric");
                              }}
                              className={`rounded-lg flex-1 min-w-0 ${
                                hasActiveGracePeriod
                                  ? 'bg-orange-600 hover:bg-orange-700 text-white'
                                  : 'bg-black hover:bg-gray-800 text-white'
                              }`}
                            >
                              <Unlock className="w-4 h-4 mr-2" />
                              {hasActiveGracePeriod ? 'Grace Access' : 'Unlock'}
                            </Button>
                          )}
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => setCurrentScreen("map")}
                            className="rounded-lg flex-shrink-0"
                          >
                            Location
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => {
                              setSelectedBookingForAgent(booking);
                              setShowAgentCoordination(true);
                            }}
                            className="rounded-lg border-blue-200 text-blue-600 hover:bg-blue-50 flex-shrink-0"
                          >
                            <User className="w-4 h-4 mr-1" />
                            Agent
                          </Button>
                          {!isExpired && !hasActiveGracePeriod && (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={async () => {
                                try {
                                  const response = await fetch(`/api/bookings/${booking.id}/cancel`, {
                                    method: 'PATCH',
                                    headers: { 'Content-Type': 'application/json' },
                                  });

                                  if (!response.ok) throw new Error('Failed to cancel booking');

                                  toast({
                                    title: "Booking Cancelled",
                                    description: `Locker ${booking.locker?.code || booking.lockerId} has been cancelled`,
                                  });

                                  // Refresh data
                                  queryClient.invalidateQueries({ queryKey: ["/api/bookings/user"] });
                                  queryClient.invalidateQueries({ queryKey: ["/api/lockers"] });
                                } catch (error) {
                                  toast({
                                    title: "Cancellation Failed",
                                    description: "Unable to cancel booking. Please try again.",
                                    variant: "destructive",
                                  });
                                }
                              }}
                              className="rounded-lg border-red-200 text-red-600 hover:bg-red-50 flex-shrink-0"
                            >
                              Cancel
                            </Button>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            ) : (
              <div className="flex items-center justify-center h-full">
                <div className="text-center">
                  <div className="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Lock className="w-10 h-10 text-gray-400" />
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No Active Lockers</h3>
                  <p className="text-gray-600 mb-6 max-w-sm">You don't have any active locker reservations</p>
                  <Button
                    onClick={() => setCurrentScreen("selection")}
                    className="bg-black hover:bg-gray-800 text-white px-8 py-3 rounded-xl active:scale-95 transition-transform"
                  >
                    Reserve a Locker
                  </Button>
                </div>
              </div>
            )}
          </div>
          {renderBottomNav()}
        </>
      )}

      {currentScreen === "history" && (
        <>
          <div className="bg-white px-4 py-4 border-b border-gray-100 flex items-center">
            <button
              onClick={() => setCurrentScreen("dashboard")}
              className="mr-4 p-2 hover:bg-gray-100 rounded-full transition-colors"
            >
              <ArrowLeft className="w-5 h-5" />
            </button>
            <h1 className="text-lg font-semibold">History</h1>
          </div>
          <div className="flex-1 p-4 pb-24 overflow-y-auto">
            {Array.isArray(userBookingHistory) && userBookingHistory.length > 0 ? (
              <div className="space-y-6">
                {/* Summary Stats */}
                <div className="grid grid-cols-2 gap-4">
                  <Card className="rounded-xl shadow-sm">
                    <CardContent className="p-4 text-center">
                      <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-2">
                        <CheckCircle2 className="w-5 h-5 text-green-600" />
                      </div>
                      <div className="text-2xl font-bold text-green-600">
                        {userBookingHistory.filter((b: any) => b.status === 'completed' || b.status === 'redeemed').length}
                      </div>
                      <div className="text-xs text-gray-600">Completed</div>
                    </CardContent>
                  </Card>
                  <Card className="rounded-xl shadow-sm">
                    <CardContent className="p-4 text-center">
                      <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-2">
                        <History className="w-5 h-5 text-blue-600" />
                      </div>
                      <div className="text-2xl font-bold text-blue-600">
                        {userBookingHistory.length}
                      </div>
                      <div className="text-xs text-gray-600">Total Bookings</div>
                    </CardContent>
                  </Card>
                </div>

                {/* History List */}
                <div className="space-y-4">
                  {userBookingHistory.map((booking: any) => {
                    const isCompleted = booking.status === 'completed' || booking.status === 'redeemed';
                    const isRedeemed = booking.status === 'redeemed';
                    const isCancelled = booking.status === 'cancelled';
                    const isExpired = booking.status === 'expired';

                    let statusColor = 'bg-gray-100 text-gray-800';
                    let iconColor = 'text-gray-600';
                    let bgColor = 'bg-gray-100';
                    let statusText = booking.status;
                    let StatusIcon = History;

                    if (isRedeemed) {
                      statusColor = 'bg-green-100 text-green-800';
                      iconColor = 'text-green-600';
                      bgColor = 'bg-green-100';
                      statusText = 'Redeemed';
                      StatusIcon = CheckCircle2;
                    } else if (isCompleted) {
                      statusColor = 'bg-blue-100 text-blue-800';
                      iconColor = 'text-blue-600';
                      bgColor = 'bg-blue-100';
                      statusText = 'Completed';
                      StatusIcon = CheckCircle2;
                    } else if (isCancelled) {
                      statusColor = 'bg-red-100 text-red-800';
                      iconColor = 'text-red-600';
                      bgColor = 'bg-red-100';
                      statusText = 'Cancelled';
                      StatusIcon = X;
                    } else if (isExpired) {
                      statusColor = 'bg-orange-100 text-orange-800';
                      iconColor = 'text-orange-600';
                      bgColor = 'bg-orange-100';
                      statusText = 'Expired';
                      StatusIcon = Clock;
                    }

                    return (
                      <Card key={booking.id} className="rounded-xl shadow-sm">
                        <CardContent className="p-4">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-3">
                              <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${bgColor}`}>
                                <StatusIcon className={`w-6 h-6 ${iconColor}`} />
                              </div>
                              <div>
                                <h4 className="font-medium text-gray-900">
                                  Locker {booking.locker?.code || booking.lockerId}
                                </h4>
                                <p className="text-sm text-gray-500">
                                  {booking.locker?.location?.name || 'Main Building'}
                                </p>
                                <p className="text-xs text-gray-400">
                                  {new Date(booking.startTime).toLocaleDateString()} - {new Date(booking.endTime).toLocaleDateString()}
                                </p>
                                {booking.agentId && (
                                  <div className="flex items-center mt-1">
                                    <User className="w-3 h-3 text-blue-500 mr-1" />
                                    <span className="text-xs text-blue-600">Agent Assisted</span>
                                  </div>
                                )}
                              </div>
                            </div>
                            <div className="text-right">
                              <div className={`px-2 py-1 rounded-full text-xs font-medium ${statusColor}`}>
                                {statusText}
                              </div>
                              <p className="text-sm font-medium text-gray-900 mt-1">
                                ${booking.totalCost || '5.00'}
                              </p>
                              {booking.agentCommission && booking.agentCommission > 0 && (
                                <p className="text-xs text-gray-500">
                                  +${booking.agentCommission} agent fee
                                </p>
                              )}
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    );
                  })}
                </div>
              </div>
            ) : (
              <div className="flex items-center justify-center h-full">
                <div className="text-center max-w-sm mx-auto px-4">
                  <div className="w-24 h-24 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <History className="w-12 h-12 text-blue-500" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">No History Yet</h3>
                  <p className="text-gray-600 mb-6 leading-relaxed">
                    Your completed locker rentals and booking history will appear here once you start using our services.
                  </p>
                  <div className="space-y-3">
                    <Button
                      onClick={() => setCurrentScreen("selection")}
                      className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 rounded-xl active:scale-95 transition-transform"
                    >
                      <Package className="w-5 h-5 mr-2" />
                      Book Your First Locker
                    </Button>
                    <Button
                      onClick={() => setCurrentScreen("map")}
                      variant="outline"
                      className="w-full py-3 rounded-xl border-2 hover:bg-gray-50 transition-colors"
                    >
                      <MapPin className="w-5 h-5 mr-2" />
                      Find Nearby Locations
                    </Button>
                  </div>
                  <div className="mt-6 p-4 bg-blue-50 rounded-lg">
                    <p className="text-sm text-blue-800">
                      <strong>💡 Tip:</strong> Your history will include completed rentals, cancellations, and payment records for easy tracking.
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>
          {renderBottomNav()}
        </>
      )}

      {currentScreen === "scan" && (
        <>
          <div className="bg-white px-4 py-4 border-b border-gray-100 flex items-center">
            <button
              onClick={() => setCurrentScreen("dashboard")}
              className="mr-4 p-2 hover:bg-gray-100 rounded-full transition-colors"
            >
              <ArrowLeft className="w-5 h-5" />
            </button>
            <h1 className="text-lg font-semibold">QR Scanner</h1>
          </div>
          <div className="flex-1 p-4 pb-24 overflow-y-auto">
            <div className="space-y-6">
              {/* QR Scanner Section */}
              <Card className="rounded-xl shadow-sm">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <QrCode className="w-5 h-5" />
                    <span>Scan QR Code</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-gray-600 text-sm">
                    Scan a QR code to unlock your locker or confirm a booking
                  </p>
                  <Button
                    onClick={() => openScanner(handleQRScan)}
                    className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 rounded-xl"
                  >
                    <QrCode className="w-5 h-5 mr-2" />
                    Open Camera Scanner
                  </Button>
                </CardContent>
              </Card>

              {/* Biometric Unlock Section */}
              <Card className="rounded-xl shadow-sm">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Fingerprint className="w-5 h-5" />
                    <span>Biometric Unlock</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-gray-600 text-sm">
                    Use your fingerprint or face ID to securely unlock your locker
                  </p>
                  <Button
                    onClick={() => setCurrentScreen("biometric")}
                    variant="outline"
                    className="w-full py-3 rounded-xl"
                  >
                    <Fingerprint className="w-5 h-5 mr-2" />
                    Start Biometric Unlock
                  </Button>
                </CardContent>
              </Card>

              {/* Quick Actions */}
              <Card className="rounded-xl shadow-sm">
                <CardHeader>
                  <CardTitle>Quick Actions</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <Button
                    onClick={() => setCurrentScreen("my-lockers")}
                    variant="outline"
                    className="w-full py-3 rounded-xl flex items-center justify-center space-x-2"
                  >
                    <Lock className="w-5 h-5" />
                    <span>My Active Lockers</span>
                  </Button>
                  <Button
                    onClick={() => setCurrentScreen("map")}
                    variant="outline"
                    className="w-full py-3 rounded-xl flex items-center justify-center space-x-2"
                  >
                    <MapPin className="w-5 h-5" />
                    <span>Find Nearby Lockers</span>
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
          {renderBottomNav()}
        </>
      )}

      {currentScreen === "profile" && (
        <>
          <div className="bg-white px-4 py-4 border-b border-gray-100 flex items-center">
            <button
              onClick={() => setCurrentScreen("dashboard")}
              className="mr-4 p-2 hover:bg-gray-100 rounded-full transition-colors"
            >
              <ArrowLeft className="w-5 h-5" />
            </button>
            <h1 className="text-lg font-semibold">Profile</h1>
          </div>
          <div className="flex-1 p-4 pb-24 overflow-y-auto">
            <Card className="mb-6 rounded-xl shadow-sm">
              <CardContent className="p-6">
                <div className="flex items-center space-x-4">
                  <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                    <User className="w-8 h-8 text-white" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold">{(currentUser as any)?.username || 'Alex'}</h3>
                    <p className="text-gray-600">Regular User</p>
                    <div className="flex items-center space-x-1 mt-1">
                      <Star className="w-4 h-4 text-yellow-500" />
                      <span className="text-sm">4.9 rating</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <div className="space-y-4">
              <Card className="rounded-xl shadow-sm">
                <CardContent className="p-4">
                  <h4 className="font-medium mb-3">Notifications</h4>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Push notifications</span>
                    <button
                      onClick={() => {
                        setPushNotificationsEnabled(!pushNotificationsEnabled);
                        toast({
                          title: "Settings Updated",
                          description: `Push notifications ${!pushNotificationsEnabled ? 'enabled' : 'disabled'}`,
                        });
                      }}
                      className="relative"
                    >
                      <div className={`w-12 h-6 rounded-full shadow-inner transition-colors ${
                        pushNotificationsEnabled ? 'bg-blue-600' : 'bg-gray-300'
                      }`}>
                        <div className={`absolute top-1 w-4 h-4 bg-white rounded-full shadow transition-transform ${
                          pushNotificationsEnabled ? 'right-1' : 'left-1'
                        }`}></div>
                      </div>
                    </button>
                  </div>
                </CardContent>
              </Card>

              <Card className="rounded-xl shadow-sm">
                <CardContent className="p-4">
                  <h4 className="font-medium mb-3">Security</h4>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Biometric unlock</span>
                      <button
                        onClick={() => {
                          setBiometricUnlockEnabled(!biometricUnlockEnabled);
                          toast({
                            title: "Security Settings Updated",
                            description: `Biometric unlock ${!biometricUnlockEnabled ? 'enabled' : 'disabled'}`,
                          });
                        }}
                        className="relative"
                      >
                        <div className={`w-12 h-6 rounded-full shadow-inner transition-colors ${
                          biometricUnlockEnabled ? 'bg-green-600' : 'bg-gray-300'
                        }`}>
                          <div className={`absolute top-1 w-4 h-4 bg-white rounded-full shadow transition-transform ${
                            biometricUnlockEnabled ? 'right-1' : 'left-1'
                          }`}></div>
                        </div>
                      </button>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Two-factor auth</span>
                      <button
                        onClick={() => {
                          setTwoFactorAuthEnabled(!twoFactorAuthEnabled);
                          toast({
                            title: "Security Settings Updated",
                            description: `Two-factor authentication ${!twoFactorAuthEnabled ? 'enabled' : 'disabled'}`,
                          });
                        }}
                        className="relative"
                      >
                        <div className={`w-12 h-6 rounded-full shadow-inner transition-colors ${
                          twoFactorAuthEnabled ? 'bg-green-600' : 'bg-gray-300'
                        }`}>
                          <div className={`absolute top-1 w-4 h-4 bg-white rounded-full shadow transition-transform ${
                            twoFactorAuthEnabled ? 'right-1' : 'left-1'
                          }`}></div>
                        </div>
                      </button>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Button
                variant="outline"
                onClick={() => setCurrentScreen("premium")}
                className="w-full py-3 rounded-xl border-2 hover:bg-yellow-50 hover:border-yellow-200 hover:text-yellow-600 transition-colors flex items-center justify-center space-x-2"
              >
                <Crown className="w-5 h-5" />
                <span>Premium Features</span>
              </Button>

              <Button
                variant="outline"
                onClick={() => setShowSupportChat(true)}
                className="w-full py-3 rounded-xl border-2 hover:bg-blue-50 hover:border-blue-200 hover:text-blue-600 transition-colors flex items-center justify-center space-x-2"
              >
                <MessageCircle className="w-5 h-5" />
                <span>Contact Support</span>
              </Button>

              <Button
                variant="outline"
                onClick={handleLogout}
                className="w-full py-3 rounded-xl border-2 hover:bg-red-50 hover:border-red-200 hover:text-red-600 transition-colors"
              >
                Sign Out
              </Button>
            </div>
          </div>
          {renderBottomNav()}
        </>
      )}

      {currentScreen === "premium" && (
        <>
          <div className="bg-white px-4 py-4 border-b border-gray-100 flex items-center">
            <button
              onClick={() => setCurrentScreen("profile")}
              className="mr-4 p-2 hover:bg-gray-100 rounded-full transition-colors"
            >
              <ArrowLeft className="w-5 h-5" />
            </button>
            <h1 className="text-lg font-semibold">Premium Features</h1>
          </div>
          <div className="flex-1 pb-24 overflow-y-auto">
            <PremiumFeatures
              currentUser={currentUser}
              onUpgrade={() => setShowPremiumUpgrade(true)}
            />
          </div>
          {renderBottomNav()}
        </>
      )}

      {currentScreen === "subscription" && (
        <>
          <div className="bg-white px-4 py-4 border-b border-gray-100 flex items-center">
            <button
              onClick={() => setCurrentScreen("premium")}
              className="mr-4 p-2 hover:bg-gray-100 rounded-full transition-colors"
            >
              <ArrowLeft className="w-5 h-5" />
            </button>
            <h1 className="text-lg font-semibold">Subscription Plans</h1>
          </div>
          <div className="flex-1 pb-24 overflow-y-auto">
            <PremiumSubscription
              currentUser={currentUser}
              onSubscribe={handlePremiumUpgrade}
              onCancel={() => setCurrentScreen("premium")}
            />
          </div>
          {renderBottomNav()}
        </>
      )}

      {/* Date Time Picker Modal */}
      {showDateTimePicker && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
          <DateTimePicker
            onDateTimeSelect={handleDateTimeSelect}
            onCancel={handleDateTimeCancel}
            lockerCode={selectedLockerData?.size}
          />
        </div>
      )}

      {/* QR Scanner Component */}
      {ScannerComponent}

      {/* Support Chat Modal */}
      {showSupportChat && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg w-full max-w-md h-96">
            <SupportChat
              currentUser={currentUser}
              onClose={() => setShowSupportChat(false)}
            />
          </div>
        </div>
      )}

      {/* Notification Center Modal */}
      {showNotifications && (
        <NotificationCenter
          notifications={displayNotifications}
          unreadCount={displayUnreadCount}
          onMarkAsRead={markAsRead}
          onMarkAllAsRead={markAllAsRead}
          onClearNotification={clearNotification}
          onClearAll={clearAllNotifications}
          onClose={() => setShowNotifications(false)}
        />
      )}

      {/* Premium Upgrade Modal */}
      <PremiumUpgradeModal
        isOpen={showPremiumUpgrade}
        onClose={() => setShowPremiumUpgrade(false)}
        currentUser={currentUser}
        onUpgrade={handlePremiumUpgrade}
        triggerFeature={premiumTriggerFeature}
      />

      {/* Restriction Modal */}
      <RestrictionModal
        isOpen={showRestrictionModal}
        reason={restrictionReason}
        onContactSupport={() => {
          setShowRestrictionModal(false);
          setShowSupportModal(true);
        }}
        onClose={() => setShowRestrictionModal(false)}
      />

      {/* Support Modal */}
      <SupportModal
        isOpen={showSupportModal}
        onClose={() => setShowSupportModal(false)}
        userType="user"
      />

      {/* Expired Locker Dialog */}
      {showExpiredLockerDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg w-full max-w-md p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Locker Expired</h3>
              <button
                onClick={() => setShowExpiredLockerDialog(null)}
                className="p-2 hover:bg-gray-100 rounded-full transition-colors"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            <div className="text-center mb-6">
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Lock className="w-8 h-8 text-red-600" />
              </div>
              <h4 className="text-lg font-medium text-gray-900 mb-2">
                Locker {showExpiredLockerDialog.locker?.code || showExpiredLockerDialog.lockerId} is Expired
              </h4>
              <p className="text-gray-600 text-sm">
                Your rental time has ended and you were unable to retrieve your items.
                Please contact our support team for assistance with accessing your belongings.
              </p>
            </div>

            {/* Rating Section */}
            <div className="mb-6 p-4 bg-gray-50 rounded-lg">
              <h5 className="font-medium text-gray-700 mb-3">Rate Your Experience</h5>
              <div className="flex items-center justify-center space-x-2 mb-3">
                {[1, 2, 3, 4, 5].map((value) => (
                  <button
                    key={value}
                    onClick={() => setExpiredLockerRating(value)}
                    className="focus:outline-none"
                  >
                    <Star
                      className={`w-6 h-6 ${
                        value <= expiredLockerRating ? "text-yellow-400 fill-current" : "text-gray-300"
                      }`}
                    />
                  </button>
                ))}
              </div>
              <Textarea
                placeholder="How was your overall locker experience?"
                value={expiredLockerFeedback}
                onChange={(e) => setExpiredLockerFeedback(e.target.value)}
                className="mb-3"
                rows={3}
              />
              {expiredLockerRating > 0 && (
                <Button
                  onClick={async () => {
                    try {
                      const response = await fetch("/api/feedback", {
                        method: "POST",
                        headers: { "Content-Type": "application/json" },
                        body: JSON.stringify({
                          rating: expiredLockerRating,
                          feedback: expiredLockerFeedback,
                          type: "expired_locker_experience",
                          bookingId: showExpiredLockerDialog.id,
                          timestamp: Date.now(),
                        }),
                      });

                      if (!response.ok) throw new Error('Failed to submit feedback');

                      toast({
                        title: "Feedback Submitted",
                        description: "Thank you for rating your experience!",
                      });

                      setExpiredLockerRating(0);
                      setExpiredLockerFeedback("");
                    } catch (error) {
                      toast({
                        title: "Submission Failed",
                        description: "Failed to submit feedback. Please try again.",
                        variant: "destructive",
                      });
                    }
                  }}
                  className="w-full mb-3"
                  variant="outline"
                  size="sm"
                >
                  Submit Rating
                </Button>
              )}
            </div>

            <div className="space-y-3">
              <Button
                onClick={() => {
                  setShowExpiredLockerDialog(null);
                  setShowSupportChat(true);
                }}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 rounded-lg flex items-center justify-center space-x-2"
              >
                <MessageCircle className="w-5 h-5" />
                <span>Contact Support Chat</span>
              </Button>

              <div className="grid grid-cols-2 gap-3">
                <Button
                  onClick={() => {
                    window.location.href = 'tel:+1234567890';
                  }}
                  variant="outline"
                  className="py-3 rounded-lg flex items-center justify-center space-x-2"
                >
                  <Phone className="w-4 h-4" />
                  <span>Call</span>
                </Button>

                <Button
                  onClick={() => {
                    window.location.href = 'mailto:<EMAIL>?subject=Expired Locker Assistance&body=I need help accessing my items from expired locker ' + (showExpiredLockerDialog.locker?.code || showExpiredLockerDialog.lockerId);
                  }}
                  variant="outline"
                  className="py-3 rounded-lg flex items-center justify-center space-x-2"
                >
                  <Mail className="w-4 h-4" />
                  <span>Email</span>
                </Button>
              </div>
            </div>

            <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
              <p className="text-xs text-yellow-800">
                <strong>Note:</strong> Additional fees may apply for extended storage beyond the rental period.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Agent Coordination Modal */}
      {showAgentCoordination && selectedBookingForAgent && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg w-full max-w-md h-[600px] flex flex-col shadow-2xl">
            <DeliveryCoordination
              bookingId={selectedBookingForAgent.id}
              currentUserId={(currentUser as any)?.id}
              onClose={() => {
                setShowAgentCoordination(false);
                setSelectedBookingForAgent(null);
              }}
            />
          </div>
        </div>
      )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function UserApp() {
  return (
    <ErrorBoundary>
      <UserAppContent />
    </ErrorBoundary>
  );
}
