// Mobile-specific features using Capacitor plugins

import { Camera, CameraResultType, CameraSource } from '@capacitor/camera';
import { Geolocation } from '@capacitor/geolocation';
import { PushNotifications } from '@capacitor/push-notifications';
import { LocalNotifications } from '@capacitor/local-notifications';
import { Haptics, ImpactStyle } from '@capacitor/haptics';
import { Device } from '@capacitor/device';
import { Network } from '@capacitor/network';
import { StatusBar, Style } from '@capacitor/status-bar';

export class MobileFeatures {
  private static instance: MobileFeatures;
  private isNative = false;

  constructor() {
    this.checkIfNative();
  }

  static getInstance(): MobileFeatures {
    if (!MobileFeatures.instance) {
      MobileFeatures.instance = new MobileFeatures();
    }
    return MobileFeatures.instance;
  }

  private async checkIfNative() {
    try {
      const info = await Device.getInfo();
      this.isNative = info.platform !== 'web';
    } catch {
      this.isNative = false;
    }
  }

  // Camera Features
  async scanQRCode(): Promise<string | null> {
    if (!this.isNative) {
      // Enhanced web QR scanning using getUserMedia
      try {
        const stream = await navigator.mediaDevices.getUserMedia({
          video: {
            facingMode: { ideal: 'environment' },
            width: { ideal: 1280 },
            height: { ideal: 720 }
          }
        });

        // Create video element for QR scanning
        return new Promise((resolve) => {
          const video = document.createElement('video');
          const canvas = document.createElement('canvas');
          const ctx = canvas.getContext('2d');

          video.srcObject = stream;
          video.setAttribute('playsinline', 'true');
          video.play();

          // Create a simple QR scanning interface
          const scanContainer = document.createElement('div');
          scanContainer.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: black;
            z-index: 10000;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
          `;

          video.style.cssText = `
            width: 100%;
            height: 70%;
            object-fit: cover;
          `;

          const instructions = document.createElement('div');
          instructions.innerHTML = `
            <p style="color: white; text-align: center; margin: 20px;">
              Point camera at QR code<br>
              <button id="close-scanner" style="margin-top: 10px; padding: 10px 20px; background: #ff4444; color: white; border: none; border-radius: 5px;">Close</button>
            </p>
          `;

          scanContainer.appendChild(video);
          scanContainer.appendChild(instructions);
          document.body.appendChild(scanContainer);

          // Close button functionality
          document.getElementById('close-scanner')?.addEventListener('click', () => {
            stream.getTracks().forEach(track => track.stop());
            document.body.removeChild(scanContainer);
            resolve(null);
          });

          // For now, return a mock QR code after 3 seconds
          // In a real implementation, you would use a QR code library like jsQR
          setTimeout(() => {
            stream.getTracks().forEach(track => track.stop());
            document.body.removeChild(scanContainer);
            resolve('LOCKER:A001:UNLOCK'); // Mock QR code data
          }, 3000);
        });
      } catch (error) {
        console.error('QR scan failed:', error);
        return null;
      }
    }

    try {
      const image = await Camera.getPhoto({
        quality: 90,
        allowEditing: false,
        resultType: CameraResultType.DataUrl,
        source: CameraSource.Camera,
        promptLabelHeader: 'Scan QR Code',
        promptLabelPhoto: 'Take Photo',
        promptLabelPicture: 'Choose from Gallery'
      });

      // Here you would integrate a QR code decoder
      // For now, return a mock result
      return image.dataUrl || null;
    } catch (error) {
      console.error('QR scan failed:', error);
      return null;
    }
  }

  async takePhoto(options?: { quality?: number; allowEditing?: boolean }): Promise<string | null> {
    if (!this.isNative) {
      // Enhanced web fallback using getUserMedia with better mobile support
      try {
        // Check if we're on mobile and prefer back camera
        const constraints = {
          video: {
            facingMode: { ideal: 'environment' }, // Prefer back camera
            width: { ideal: 1920 },
            height: { ideal: 1080 }
          }
        };

        const stream = await navigator.mediaDevices.getUserMedia(constraints);

        // Create a more robust photo capture interface
        return new Promise((resolve, reject) => {
          const video = document.createElement('video');
          const canvas = document.createElement('canvas');
          const ctx = canvas.getContext('2d');

          video.srcObject = stream;
          video.setAttribute('playsinline', 'true'); // Important for iOS
          video.play();

          video.onloadedmetadata = () => {
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;

            // Capture frame after a short delay to ensure video is ready
            setTimeout(() => {
              if (ctx) {
                ctx.drawImage(video, 0, 0);
                const quality = (options?.quality || 90) / 100;
                const dataUrl = canvas.toDataURL('image/jpeg', quality);

                // Stop all tracks
                stream.getTracks().forEach(track => track.stop());
                resolve(dataUrl);
              } else {
                reject(new Error('Canvas context not available'));
              }
            }, 100);
          };

          video.onerror = () => {
            stream.getTracks().forEach(track => track.stop());
            reject(new Error('Video loading failed'));
          };
        });
      } catch (error) {
        console.error('Camera access failed:', error);

        // Fallback to file input for older browsers
        return this.fallbackPhotoCapture();
      }
    }

    try {
      const image = await Camera.getPhoto({
        quality: options?.quality || 90,
        allowEditing: options?.allowEditing || false,
        resultType: CameraResultType.DataUrl,
        source: CameraSource.Camera
      });

      return image.dataUrl || null;
    } catch (error) {
      console.error('Photo capture failed:', error);
      return null;
    }
  }

  // Fallback photo capture using file input
  private async fallbackPhotoCapture(): Promise<string | null> {
    return new Promise((resolve) => {
      const input = document.createElement('input');
      input.type = 'file';
      input.accept = 'image/*';
      input.capture = 'environment'; // Prefer back camera on mobile

      input.onchange = (event) => {
        const file = (event.target as HTMLInputElement).files?.[0];
        if (file) {
          const reader = new FileReader();
          reader.onload = (e) => resolve(e.target?.result as string);
          reader.onerror = () => resolve(null);
          reader.readAsDataURL(file);
        } else {
          resolve(null);
        }
      };

      input.click();
    });
  }

  // Location Features
  async getCurrentLocation(): Promise<{ lat: number; lng: number } | null> {
    if (this.isNative) {
      try {
        const coordinates = await Geolocation.getCurrentPosition({
          enableHighAccuracy: true,
          timeout: 10000
        });

        return {
          lat: coordinates.coords.latitude,
          lng: coordinates.coords.longitude
        };
      } catch (error) {
        console.error('Location access failed:', error);
        return null;
      }
    } else {
      // Use web geolocation API
      try {
        if (!('geolocation' in navigator)) {
          console.log('Geolocation not supported');
          return null;
        }

        return new Promise((resolve) => {
          navigator.geolocation.getCurrentPosition(
            (position) => {
              resolve({
                lat: position.coords.latitude,
                lng: position.coords.longitude
              });
            },
            (error) => {
              console.log('Web geolocation failed:', error.message);
              resolve(null);
            },
            {
              enableHighAccuracy: true,
              timeout: 10000,
              maximumAge: 60000
            }
          );
        });
      } catch (error) {
        console.error('Web location access failed:', error);
        return null;
      }
    }
  }

  async watchLocation(callback: (position: { lat: number; lng: number }) => void): Promise<string | null> {
    try {
      const watchId = await Geolocation.watchPosition({
        enableHighAccuracy: true,
        timeout: 10000
      }, (position) => {
        if (position) {
          callback({
            lat: position.coords.latitude,
            lng: position.coords.longitude
          });
        }
      });

      return watchId;
    } catch (error) {
      console.error('Location watching failed:', error);
      return null;
    }
  }

  async clearLocationWatch(watchId: string) {
    try {
      await Geolocation.clearWatch({ id: watchId });
    } catch (error) {
      console.error('Clear location watch failed:', error);
    }
  }

  // Push Notifications
  async setupPushNotifications(): Promise<boolean> {
    if (!this.isNative) {
      // Use web push notifications
      return this.setupWebPushNotifications();
    }

    try {
      const permission = await PushNotifications.requestPermissions();
      
      if (permission.receive === 'granted') {
        await PushNotifications.register();
        
        // Listen for registration
        PushNotifications.addListener('registration', (token) => {
          console.log('Push registration success, token: ' + token.value);
          // Send token to your server
        });

        // Listen for push notifications
        PushNotifications.addListener('pushNotificationReceived', (notification) => {
          console.log('Push notification received: ', notification);
        });

        // Listen for notification actions
        PushNotifications.addListener('pushNotificationActionPerformed', (notification) => {
          console.log('Push notification action performed', notification.actionId, notification.inputValue);
        });

        return true;
      }
    } catch (error) {
      console.error('Push notification setup failed:', error);
    }

    return false;
  }

  private async setupWebPushNotifications(): Promise<boolean> {
    if (!('Notification' in window) || !('serviceWorker' in navigator)) {
      return false;
    }

    try {
      const permission = await Notification.requestPermission();
      return permission === 'granted';
    } catch (error) {
      console.error('Web push setup failed:', error);
      return false;
    }
  }

  // Local Notifications
  async scheduleLocalNotification(title: string, body: string, delay?: number): Promise<void> {
    try {
      await LocalNotifications.schedule({
        notifications: [
          {
            title,
            body,
            id: Date.now(),
            schedule: delay ? { at: new Date(Date.now() + delay) } : undefined,
            sound: 'default',
            attachments: undefined,
            actionTypeId: '',
            extra: null
          }
        ]
      });
    } catch (error) {
      console.error('Local notification failed:', error);
    }
  }

  // Haptic Feedback
  async vibrate(style: 'light' | 'medium' | 'heavy' = 'medium'): Promise<void> {
    if (!this.isNative) {
      // Web vibration fallback
      if ('vibrate' in navigator) {
        const duration = style === 'light' ? 50 : style === 'medium' ? 100 : 200;
        navigator.vibrate(duration);
      }
      return;
    }

    try {
      const impactStyle = style === 'light' ? ImpactStyle.Light : 
                         style === 'medium' ? ImpactStyle.Medium : ImpactStyle.Heavy;
      await Haptics.impact({ style: impactStyle });
    } catch (error) {
      console.error('Haptic feedback failed:', error);
    }
  }

  // Device Info
  async getDeviceInfo(): Promise<any> {
    try {
      return await Device.getInfo();
    } catch (error) {
      console.error('Device info failed:', error);
      return null;
    }
  }

  // Network Status
  async getNetworkStatus(): Promise<{ connected: boolean; connectionType: string }> {
    try {
      const status = await Network.getStatus();
      return {
        connected: status.connected,
        connectionType: status.connectionType
      };
    } catch (error) {
      console.error('Network status failed:', error);
      return { connected: true, connectionType: 'unknown' };
    }
  }

  // Status Bar
  async setStatusBarStyle(style: 'light' | 'dark' = 'dark'): Promise<void> {
    if (!this.isNative) return;

    try {
      await StatusBar.setStyle({
        style: style === 'light' ? Style.Light : Style.Dark
      });
    } catch (error) {
      console.error('Status bar style failed:', error);
    }
  }

  async setStatusBarColor(color: string): Promise<void> {
    if (!this.isNative) return;

    try {
      await StatusBar.setBackgroundColor({ color });
    } catch (error) {
      console.error('Status bar color failed:', error);
    }
  }

  // Biometric Authentication (Web Authentication API)
  async authenticateWithBiometrics(): Promise<boolean> {
    if (!this.isNative) {
      // Use Web Authentication API for biometric authentication
      return this.webBiometricAuth();
    }

    // For native apps, you would use a biometric plugin
    // This is a placeholder for native biometric authentication
    try {
      // Placeholder for native biometric authentication
      console.log('Native biometric authentication not implemented');
      return false;
    } catch (error) {
      console.error('Biometric authentication failed:', error);
      return false;
    }
  }

  private async webBiometricAuth(): Promise<boolean> {
    if (!window.PublicKeyCredential) {
      console.log('WebAuthn not supported');
      return false;
    }

    try {
      // Check if biometric authentication is available
      const available = await PublicKeyCredential.isUserVerifyingPlatformAuthenticatorAvailable();

      if (!available) {
        console.log('Biometric authentication not available');
        return false;
      }

      // Create credential options
      const publicKeyCredentialCreationOptions: PublicKeyCredentialCreationOptions = {
        challenge: new Uint8Array(32),
        rp: {
          name: "SwiSto",
          id: window.location.hostname,
        },
        user: {
          id: new Uint8Array(16),
          name: "<EMAIL>",
          displayName: "SwiSto User",
        },
        pubKeyCredParams: [{alg: -7, type: "public-key"}],
        authenticatorSelection: {
          authenticatorAttachment: "platform",
          userVerification: "required"
        },
        timeout: 60000,
        attestation: "direct"
      };

      // Create credential
      const credential = await navigator.credentials.create({
        publicKey: publicKeyCredentialCreationOptions
      });

      return credential !== null;
    } catch (error) {
      console.error('Web biometric authentication failed:', error);
      return false;
    }
  }

  // Check if biometric authentication is available
  async isBiometricAvailable(): Promise<boolean> {
    if (!this.isNative) {
      if (!window.PublicKeyCredential) {
        return false;
      }

      try {
        return await PublicKeyCredential.isUserVerifyingPlatformAuthenticatorAvailable();
      } catch {
        return false;
      }
    }

    // For native apps, check biometric availability
    return true; // Placeholder
  }

  // Utility Methods
  isNativeApp(): boolean {
    return this.isNative;
  }

  async requestPermissions(): Promise<void> {
    // Request all necessary permissions
    await this.setupPushNotifications();

    if (this.isNative) {
      try {
        await Geolocation.requestPermissions();
      } catch (error) {
        console.error('Location permission failed:', error);
      }
    } else {
      // For web, request geolocation permission through browser API
      try {
        if ('geolocation' in navigator) {
          // This will trigger the browser's permission prompt
          await new Promise((resolve, reject) => {
            navigator.geolocation.getCurrentPosition(
              () => resolve(true),
              (error) => {
                console.log('Geolocation permission denied or unavailable:', error.message);
                resolve(true); // Don't fail the whole app if location is denied
              },
              { timeout: 5000 }
            );
          });
        }
      } catch (error) {
        console.log('Web geolocation permission request failed:', error);
      }
    }
  }
}

// Export singleton instance
export const mobileFeatures = MobileFeatures.getInstance();
