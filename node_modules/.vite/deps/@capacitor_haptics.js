import {
  ImpactStyle,
  NotificationType
} from "./chunk-UKWEPBII.js";
import {
  registerPlugin
} from "./chunk-HAUB5OVN.js";
import "./chunk-WOOG5QLI.js";

// node_modules/@capacitor/haptics/dist/esm/index.js
var Haptics = registerPlugin("Haptics", {
  web: () => import("./web-ELBWS2DR.js").then((m) => new m.HapticsWeb())
});
export {
  Haptics,
  ImpactStyle,
  NotificationType
};
//# sourceMappingURL=@capacitor_haptics.js.map
