{"version": 3, "sources": ["../../@capacitor/local-notifications/src/web.ts"], "sourcesContent": ["import { WebPlugin } from '@capacitor/core';\nimport type { PermissionState } from '@capacitor/core';\n\nimport type {\n  DeliveredNotifications,\n  EnabledResult,\n  ListChannelsResult,\n  LocalNotificationSchema,\n  LocalNotificationsPlugin,\n  PendingResult,\n  PermissionStatus,\n  ScheduleOptions,\n  ScheduleResult,\n  SettingsPermissionStatus,\n} from './definitions';\n\nexport class LocalNotificationsWeb\n  extends WebPlugin\n  implements LocalNotificationsPlugin\n{\n  protected pending: LocalNotificationSchema[] = [];\n  protected deliveredNotifications: Notification[] = [];\n\n  async getDeliveredNotifications(): Promise<DeliveredNotifications> {\n    const deliveredSchemas = [];\n    for (const notification of this.deliveredNotifications) {\n      const deliveredSchema: LocalNotificationSchema = {\n        title: notification.title,\n        id: parseInt(notification.tag),\n        body: notification.body,\n      };\n      deliveredSchemas.push(deliveredSchema);\n    }\n    return {\n      notifications: deliveredSchemas,\n    };\n  }\n  async removeDeliveredNotifications(\n    delivered: DeliveredNotifications,\n  ): Promise<void> {\n    for (const toRemove of delivered.notifications) {\n      const found = this.deliveredNotifications.find(\n        n => n.tag === String(toRemove.id),\n      );\n      found?.close();\n      this.deliveredNotifications = this.deliveredNotifications.filter(\n        () => !found,\n      );\n    }\n  }\n  async removeAllDeliveredNotifications(): Promise<void> {\n    for (const notification of this.deliveredNotifications) {\n      notification.close();\n    }\n    this.deliveredNotifications = [];\n  }\n  async createChannel(): Promise<void> {\n    throw this.unimplemented('Not implemented on web.');\n  }\n\n  async deleteChannel(): Promise<void> {\n    throw this.unimplemented('Not implemented on web.');\n  }\n\n  async listChannels(): Promise<ListChannelsResult> {\n    throw this.unimplemented('Not implemented on web.');\n  }\n\n  async schedule(options: ScheduleOptions): Promise<ScheduleResult> {\n    if (!this.hasNotificationSupport()) {\n      throw this.unavailable('Notifications not supported in this browser.');\n    }\n\n    for (const notification of options.notifications) {\n      this.sendNotification(notification);\n    }\n\n    return {\n      notifications: options.notifications.map(notification => ({\n        id: notification.id,\n      })),\n    };\n  }\n\n  async getPending(): Promise<PendingResult> {\n    return {\n      notifications: this.pending,\n    };\n  }\n\n  async registerActionTypes(): Promise<void> {\n    throw this.unimplemented('Not implemented on web.');\n  }\n\n  async cancel(pending: ScheduleResult): Promise<void> {\n    this.pending = this.pending.filter(\n      notification =>\n        !pending.notifications.find(n => n.id === notification.id),\n    );\n  }\n\n  async areEnabled(): Promise<EnabledResult> {\n    const { display } = await this.checkPermissions();\n\n    return {\n      value: display === 'granted',\n    };\n  }\n\n  async changeExactNotificationSetting(): Promise<SettingsPermissionStatus> {\n    throw this.unimplemented('Not implemented on web.');\n  }\n\n  async checkExactNotificationSetting(): Promise<SettingsPermissionStatus> {\n    throw this.unimplemented('Not implemented on web.');\n  }\n\n  async requestPermissions(): Promise<PermissionStatus> {\n    if (!this.hasNotificationSupport()) {\n      throw this.unavailable('Notifications not supported in this browser.');\n    }\n\n    const display = this.transformNotificationPermission(\n      await Notification.requestPermission(),\n    );\n\n    return { display };\n  }\n\n  async checkPermissions(): Promise<PermissionStatus> {\n    if (!this.hasNotificationSupport()) {\n      throw this.unavailable('Notifications not supported in this browser.');\n    }\n\n    const display = this.transformNotificationPermission(\n      Notification.permission,\n    );\n\n    return { display };\n  }\n\n  protected hasNotificationSupport = (): boolean => {\n    if (!('Notification' in window) || !Notification.requestPermission) {\n      return false;\n    }\n\n    if (Notification.permission !== 'granted') {\n      // don't test for `new Notification` if permission has already been granted\n      // otherwise this sends a real notification on supported browsers\n      try {\n        new Notification('');\n      } catch (e) {\n        if (e.name == 'TypeError') {\n          return false;\n        }\n      }\n    }\n\n    return true;\n  };\n\n  protected transformNotificationPermission(\n    permission: NotificationPermission,\n  ): PermissionState {\n    switch (permission) {\n      case 'granted':\n        return 'granted';\n      case 'denied':\n        return 'denied';\n      default:\n        return 'prompt';\n    }\n  }\n\n  protected sendPending(): void {\n    const toRemove: LocalNotificationSchema[] = [];\n    const now = new Date().getTime();\n\n    for (const notification of this.pending) {\n      if (\n        notification.schedule?.at &&\n        notification.schedule.at.getTime() <= now\n      ) {\n        this.buildNotification(notification);\n        toRemove.push(notification);\n      }\n    }\n\n    this.pending = this.pending.filter(\n      notification => !toRemove.find(n => n === notification),\n    );\n  }\n\n  protected sendNotification(notification: LocalNotificationSchema): void {\n    if (notification.schedule?.at) {\n      const diff = notification.schedule.at.getTime() - new Date().getTime();\n\n      this.pending.push(notification);\n      setTimeout(() => {\n        this.sendPending();\n      }, diff);\n      return;\n    }\n    this.buildNotification(notification);\n  }\n\n  protected buildNotification(\n    notification: LocalNotificationSchema,\n  ): Notification {\n    const localNotification = new Notification(notification.title, {\n      body: notification.body,\n      tag: String(notification.id),\n    });\n    localNotification.addEventListener(\n      'click',\n      this.onClick.bind(this, notification),\n      false,\n    );\n    localNotification.addEventListener(\n      'show',\n      this.onShow.bind(this, notification),\n      false,\n    );\n    localNotification.addEventListener(\n      'close',\n      () => {\n        this.deliveredNotifications = this.deliveredNotifications.filter(\n          () => !this,\n        );\n      },\n      false,\n    );\n    this.deliveredNotifications.push(localNotification);\n    return localNotification;\n  }\n\n  protected onClick(notification: LocalNotificationSchema): void {\n    const data = {\n      actionId: 'tap',\n      notification,\n    };\n    this.notifyListeners('localNotificationActionPerformed', data);\n  }\n\n  protected onShow(notification: LocalNotificationSchema): void {\n    this.notifyListeners('localNotificationReceived', notification);\n  }\n}\n"], "mappings": ";;;;;;AAgBM,IAAO,wBAAP,cACI,UAAS;EADnB,cAAA;;AAIY,SAAA,UAAqC,CAAA;AACrC,SAAA,yBAAyC,CAAA;AAwHzC,SAAA,yBAAyB,MAAc;AAC/C,UAAI,EAAE,kBAAkB,WAAW,CAAC,aAAa,mBAAmB;AAClE,eAAO;;AAGT,UAAI,aAAa,eAAe,WAAW;AAGzC,YAAI;AACF,cAAI,aAAa,EAAE;iBACZ,GAAG;AACV,cAAI,EAAE,QAAQ,aAAa;AACzB,mBAAO;;;;AAKb,aAAO;IACT;EAwFF;EAhOE,MAAM,4BAAyB;AAC7B,UAAM,mBAAmB,CAAA;AACzB,eAAW,gBAAgB,KAAK,wBAAwB;AACtD,YAAM,kBAA2C;QAC/C,OAAO,aAAa;QACpB,IAAI,SAAS,aAAa,GAAG;QAC7B,MAAM,aAAa;;AAErB,uBAAiB,KAAK,eAAe;;AAEvC,WAAO;MACL,eAAe;;EAEnB;EACA,MAAM,6BACJ,WAAiC;AAEjC,eAAW,YAAY,UAAU,eAAe;AAC9C,YAAM,QAAQ,KAAK,uBAAuB,KACxC,OAAK,EAAE,QAAQ,OAAO,SAAS,EAAE,CAAC;AAEpC,gBAAK,QAAL,UAAK,SAAA,SAAL,MAAO,MAAK;AACZ,WAAK,yBAAyB,KAAK,uBAAuB,OACxD,MAAM,CAAC,KAAK;;EAGlB;EACA,MAAM,kCAA+B;AACnC,eAAW,gBAAgB,KAAK,wBAAwB;AACtD,mBAAa,MAAK;;AAEpB,SAAK,yBAAyB,CAAA;EAChC;EACA,MAAM,gBAAa;AACjB,UAAM,KAAK,cAAc,yBAAyB;EACpD;EAEA,MAAM,gBAAa;AACjB,UAAM,KAAK,cAAc,yBAAyB;EACpD;EAEA,MAAM,eAAY;AAChB,UAAM,KAAK,cAAc,yBAAyB;EACpD;EAEA,MAAM,SAAS,SAAwB;AACrC,QAAI,CAAC,KAAK,uBAAsB,GAAI;AAClC,YAAM,KAAK,YAAY,8CAA8C;;AAGvE,eAAW,gBAAgB,QAAQ,eAAe;AAChD,WAAK,iBAAiB,YAAY;;AAGpC,WAAO;MACL,eAAe,QAAQ,cAAc,IAAI,mBAAiB;QACxD,IAAI,aAAa;QACjB;;EAEN;EAEA,MAAM,aAAU;AACd,WAAO;MACL,eAAe,KAAK;;EAExB;EAEA,MAAM,sBAAmB;AACvB,UAAM,KAAK,cAAc,yBAAyB;EACpD;EAEA,MAAM,OAAO,SAAuB;AAClC,SAAK,UAAU,KAAK,QAAQ,OAC1B,kBACE,CAAC,QAAQ,cAAc,KAAK,OAAK,EAAE,OAAO,aAAa,EAAE,CAAC;EAEhE;EAEA,MAAM,aAAU;AACd,UAAM,EAAE,QAAO,IAAK,MAAM,KAAK,iBAAgB;AAE/C,WAAO;MACL,OAAO,YAAY;;EAEvB;EAEA,MAAM,iCAA8B;AAClC,UAAM,KAAK,cAAc,yBAAyB;EACpD;EAEA,MAAM,gCAA6B;AACjC,UAAM,KAAK,cAAc,yBAAyB;EACpD;EAEA,MAAM,qBAAkB;AACtB,QAAI,CAAC,KAAK,uBAAsB,GAAI;AAClC,YAAM,KAAK,YAAY,8CAA8C;;AAGvE,UAAM,UAAU,KAAK,gCACnB,MAAM,aAAa,kBAAiB,CAAE;AAGxC,WAAO,EAAE,QAAO;EAClB;EAEA,MAAM,mBAAgB;AACpB,QAAI,CAAC,KAAK,uBAAsB,GAAI;AAClC,YAAM,KAAK,YAAY,8CAA8C;;AAGvE,UAAM,UAAU,KAAK,gCACnB,aAAa,UAAU;AAGzB,WAAO,EAAE,QAAO;EAClB;EAsBU,gCACR,YAAkC;AAElC,YAAQ,YAAY;MAClB,KAAK;AACH,eAAO;MACT,KAAK;AACH,eAAO;MACT;AACE,eAAO;;EAEb;EAEU,cAAW;;AACnB,UAAM,WAAsC,CAAA;AAC5C,UAAM,OAAM,oBAAI,KAAI,GAAG,QAAO;AAE9B,eAAW,gBAAgB,KAAK,SAAS;AACvC,YACE,KAAA,aAAa,cAAQ,QAAA,OAAA,SAAA,SAAA,GAAE,OACvB,aAAa,SAAS,GAAG,QAAO,KAAM,KACtC;AACA,aAAK,kBAAkB,YAAY;AACnC,iBAAS,KAAK,YAAY;;;AAI9B,SAAK,UAAU,KAAK,QAAQ,OAC1B,kBAAgB,CAAC,SAAS,KAAK,OAAK,MAAM,YAAY,CAAC;EAE3D;EAEU,iBAAiB,cAAqC;;AAC9D,SAAA,KAAI,aAAa,cAAQ,QAAA,OAAA,SAAA,SAAA,GAAE,IAAI;AAC7B,YAAM,OAAO,aAAa,SAAS,GAAG,QAAO,KAAK,oBAAI,KAAI,GAAG,QAAO;AAEpE,WAAK,QAAQ,KAAK,YAAY;AAC9B,iBAAW,MAAK;AACd,aAAK,YAAW;MAClB,GAAG,IAAI;AACP;;AAEF,SAAK,kBAAkB,YAAY;EACrC;EAEU,kBACR,cAAqC;AAErC,UAAM,oBAAoB,IAAI,aAAa,aAAa,OAAO;MAC7D,MAAM,aAAa;MACnB,KAAK,OAAO,aAAa,EAAE;KAC5B;AACD,sBAAkB,iBAChB,SACA,KAAK,QAAQ,KAAK,MAAM,YAAY,GACpC,KAAK;AAEP,sBAAkB,iBAChB,QACA,KAAK,OAAO,KAAK,MAAM,YAAY,GACnC,KAAK;AAEP,sBAAkB,iBAChB,SACA,MAAK;AACH,WAAK,yBAAyB,KAAK,uBAAuB,OACxD,MAAM,CAAC,IAAI;IAEf,GACA,KAAK;AAEP,SAAK,uBAAuB,KAAK,iBAAiB;AAClD,WAAO;EACT;EAEU,QAAQ,cAAqC;AACrD,UAAM,OAAO;MACX,UAAU;MACV;;AAEF,SAAK,gBAAgB,oCAAoC,IAAI;EAC/D;EAEU,OAAO,cAAqC;AACpD,SAAK,gBAAgB,6BAA6B,YAAY;EAChE;;", "names": []}