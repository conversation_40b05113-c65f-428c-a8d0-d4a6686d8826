import {
  registerPlugin
} from "./chunk-HAUB5OVN.js";
import "./chunk-WOOG5QLI.js";

// node_modules/@capacitor/status-bar/dist/esm/definitions.js
var Style;
(function(Style2) {
  Style2["Dark"] = "DARK";
  Style2["Light"] = "LIGHT";
  Style2["Default"] = "DEFAULT";
})(Style || (Style = {}));
var Animation;
(function(Animation2) {
  Animation2["None"] = "NONE";
  Animation2["Slide"] = "SLIDE";
  Animation2["Fade"] = "FADE";
})(Animation || (Animation = {}));
var StatusBarAnimation = Animation;
var StatusBarStyle = Style;

// node_modules/@capacitor/status-bar/dist/esm/index.js
var StatusBar = registerPlugin("StatusBar");
export {
  Animation,
  StatusBar,
  StatusBarAnimation,
  StatusBarStyle,
  Style
};
//# sourceMappingURL=@capacitor_status-bar.js.map
