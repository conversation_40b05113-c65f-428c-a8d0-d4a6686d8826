{"hash": "48bb044d", "configHash": "596248e8", "lockfileHash": "b48db763", "browserHash": "1d6421aa", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "87583873", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "39257ad2", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "d35c249f", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "de5fc15c", "needsInterop": true}, "@capacitor/camera": {"src": "../../@capacitor/camera/dist/esm/index.js", "file": "@capacitor_camera.js", "fileHash": "56fbddfb", "needsInterop": false}, "@capacitor/device": {"src": "../../@capacitor/device/dist/esm/index.js", "file": "@capacitor_device.js", "fileHash": "ae61d332", "needsInterop": false}, "@capacitor/geolocation": {"src": "../../@capacitor/geolocation/dist/esm/index.js", "file": "@capacitor_geolocation.js", "fileHash": "6e6a0d0e", "needsInterop": false}, "@capacitor/haptics": {"src": "../../@capacitor/haptics/dist/esm/index.js", "file": "@capacitor_haptics.js", "fileHash": "acb8612b", "needsInterop": false}, "@capacitor/local-notifications": {"src": "../../@capacitor/local-notifications/dist/esm/index.js", "file": "@capacitor_local-notifications.js", "fileHash": "9a5b2858", "needsInterop": false}, "@capacitor/network": {"src": "../../@capacitor/network/dist/esm/index.js", "file": "@capacitor_network.js", "fileHash": "b6f66830", "needsInterop": false}, "@capacitor/push-notifications": {"src": "../../@capacitor/push-notifications/dist/esm/index.js", "file": "@capacitor_push-notifications.js", "fileHash": "9055bf1f", "needsInterop": false}, "@capacitor/status-bar": {"src": "../../@capacitor/status-bar/dist/esm/index.js", "file": "@capacitor_status-bar.js", "fileHash": "f50f6417", "needsInterop": false}, "@radix-ui/react-checkbox": {"src": "../../@radix-ui/react-checkbox/dist/index.mjs", "file": "@radix-ui_react-checkbox.js", "fileHash": "b096ddbe", "needsInterop": false}, "@radix-ui/react-dialog": {"src": "../../@radix-ui/react-dialog/dist/index.mjs", "file": "@radix-ui_react-dialog.js", "fileHash": "90892533", "needsInterop": false}, "@radix-ui/react-dropdown-menu": {"src": "../../@radix-ui/react-dropdown-menu/dist/index.mjs", "file": "@radix-ui_react-dropdown-menu.js", "fileHash": "074b964c", "needsInterop": false}, "@radix-ui/react-label": {"src": "../../@radix-ui/react-label/dist/index.mjs", "file": "@radix-ui_react-label.js", "fileHash": "3180ccf6", "needsInterop": false}, "@radix-ui/react-progress": {"src": "../../@radix-ui/react-progress/dist/index.mjs", "file": "@radix-ui_react-progress.js", "fileHash": "6236e7f9", "needsInterop": false}, "@radix-ui/react-select": {"src": "../../@radix-ui/react-select/dist/index.mjs", "file": "@radix-ui_react-select.js", "fileHash": "da2bb438", "needsInterop": false}, "@radix-ui/react-slot": {"src": "../../@radix-ui/react-slot/dist/index.mjs", "file": "@radix-ui_react-slot.js", "fileHash": "12697ff7", "needsInterop": false}, "@radix-ui/react-switch": {"src": "../../@radix-ui/react-switch/dist/index.mjs", "file": "@radix-ui_react-switch.js", "fileHash": "76de891a", "needsInterop": false}, "@radix-ui/react-tabs": {"src": "../../@radix-ui/react-tabs/dist/index.mjs", "file": "@radix-ui_react-tabs.js", "fileHash": "17413a3c", "needsInterop": false}, "@radix-ui/react-toast": {"src": "../../@radix-ui/react-toast/dist/index.mjs", "file": "@radix-ui_react-toast.js", "fileHash": "d333370b", "needsInterop": false}, "@radix-ui/react-tooltip": {"src": "../../@radix-ui/react-tooltip/dist/index.mjs", "file": "@radix-ui_react-tooltip.js", "fileHash": "aa40157b", "needsInterop": false}, "@tanstack/react-query": {"src": "../../@tanstack/react-query/build/modern/index.js", "file": "@tanstack_react-query.js", "fileHash": "0882dd94", "needsInterop": false}, "class-variance-authority": {"src": "../../class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "d3eadac5", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "3a3d975f", "needsInterop": false}, "date-fns": {"src": "../../date-fns/index.mjs", "file": "date-fns.js", "fileHash": "90eadd9d", "needsInterop": false}, "leaflet": {"src": "../../leaflet/dist/leaflet-src.js", "file": "leaflet.js", "fileHash": "29b58282", "needsInterop": true}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "0c0a8aec", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "a5f6453d", "needsInterop": true}, "react-leaflet": {"src": "../../react-leaflet/lib/index.js", "file": "react-leaflet.js", "fileHash": "6dfeaee2", "needsInterop": false}, "recharts": {"src": "../../recharts/es6/index.js", "file": "recharts.js", "fileHash": "d89972bd", "needsInterop": false}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "c5fc6b9a", "needsInterop": false}, "wouter": {"src": "../../wouter/esm/index.js", "file": "wouter.js", "fileHash": "736d4b53", "needsInterop": false}}, "chunks": {"web-DJKZM54J": {"file": "web-DJKZM54J.js"}, "web-7WDZ5VTT": {"file": "web-7WDZ5VTT.js"}, "web-ELBWS2DR": {"file": "web-ELBWS2DR.js"}, "web-6QXAY6VZ": {"file": "web-6QXAY6VZ.js"}, "web-RWNTJFQJ": {"file": "web-RWNTJFQJ.js"}, "chunk-JQURRHX6": {"file": "chunk-JQURRHX6.js"}, "chunk-U7P2NEEE": {"file": "chunk-U7P2NEEE.js"}, "chunk-G6WN2CYI": {"file": "chunk-G6WN2CYI.js"}, "chunk-43G6IMPT": {"file": "chunk-43G6IMPT.js"}, "chunk-MWGJIRSI": {"file": "chunk-MWGJIRSI.js"}, "chunk-4RO2SXZU": {"file": "chunk-4RO2SXZU.js"}, "chunk-W2OT6ZD3": {"file": "chunk-W2OT6ZD3.js"}, "chunk-FUFMHE3S": {"file": "chunk-FUFMHE3S.js"}, "chunk-USGN5C6Q": {"file": "chunk-USGN5C6Q.js"}, "chunk-BCQR5QVC": {"file": "chunk-BCQR5QVC.js"}, "chunk-YYW6Y33B": {"file": "chunk-YYW6Y33B.js"}, "chunk-5R5FMENF": {"file": "chunk-5R5FMENF.js"}, "chunk-KCFVMCIE": {"file": "chunk-KCFVMCIE.js"}, "chunk-DWM4GXJY": {"file": "chunk-DWM4GXJY.js"}, "chunk-G7KMZA27": {"file": "chunk-G7KMZA27.js"}, "chunk-OXZDJRWN": {"file": "chunk-OXZDJRWN.js"}, "chunk-PY4CUOXA": {"file": "chunk-PY4CUOXA.js"}, "chunk-E7TSFT4J": {"file": "chunk-E7TSFT4J.js"}, "chunk-SXRIVT2P": {"file": "chunk-SXRIVT2P.js"}, "chunk-KBTYAULA": {"file": "chunk-KBTYAULA.js"}, "chunk-QCHXOAYK": {"file": "chunk-QCHXOAYK.js"}, "chunk-UKWEPBII": {"file": "chunk-UKWEPBII.js"}, "chunk-HAUB5OVN": {"file": "chunk-HAUB5OVN.js"}, "chunk-WOOG5QLI": {"file": "chunk-WOOG5QLI.js"}}}