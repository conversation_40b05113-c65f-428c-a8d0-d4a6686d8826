{"version": 3, "sources": ["../../@capacitor/geolocation/src/web.ts"], "sourcesContent": ["import { WebPlugin } from '@capacitor/core';\n\nimport type {\n  CallbackID,\n  GeolocationPlugin,\n  PermissionStatus,\n  Position,\n  PositionOptions,\n  WatchPositionCallback,\n} from './definitions';\n\nexport class GeolocationWeb extends WebPlugin implements GeolocationPlugin {\n  async getCurrentPosition(options?: PositionOptions): Promise<Position> {\n    return new Promise((resolve, reject) => {\n      navigator.geolocation.getCurrentPosition(\n        (pos) => {\n          resolve(pos);\n        },\n        (err) => {\n          reject(err);\n        },\n        {\n          enableHighAccuracy: false,\n          timeout: 10000,\n          maximumAge: 0,\n          ...options,\n        },\n      );\n    });\n  }\n\n  async watchPosition(options: PositionOptions, callback: WatchPositionCallback): Promise<CallbackID> {\n    const id = navigator.geolocation.watchPosition(\n      (pos) => {\n        callback(pos);\n      },\n      (err) => {\n        callback(null, err);\n      },\n      {\n        enableHighAccuracy: false,\n        timeout: 10000,\n        maximumAge: 0,\n        minimumUpdateInterval: 5000,\n        ...options,\n      },\n    );\n\n    return `${id}`;\n  }\n\n  async clearWatch(options: { id: string }): Promise<void> {\n    navigator.geolocation.clearWatch(parseInt(options.id, 10));\n  }\n\n  async checkPermissions(): Promise<PermissionStatus> {\n    if (typeof navigator === 'undefined' || !navigator.permissions) {\n      throw this.unavailable('Permissions API not available in this browser');\n    }\n\n    const permission = await navigator.permissions.query({\n      name: 'geolocation',\n    });\n    return { location: permission.state, coarseLocation: permission.state };\n  }\n\n  async requestPermissions(): Promise<PermissionStatus> {\n    throw this.unimplemented('Not implemented on web.');\n  }\n}\n\nconst Geolocation = new GeolocationWeb();\n\nexport { Geolocation };\n"], "mappings": ";;;;;;AAWM,IAAO,iBAAP,cAA8B,UAAS;EAC3C,MAAM,mBAAmB,SAAyB;AAChD,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAU;AACrC,gBAAU,YAAY,mBACpB,CAAC,QAAO;AACN,gBAAQ,GAAG;MACb,GACA,CAAC,QAAO;AACN,eAAO,GAAG;MACZ,GAAC,OAAA,OAAA,EAEC,oBAAoB,OACpB,SAAS,KACT,YAAY,EAAC,GACV,OAAO,CAAA;IAGhB,CAAC;EACH;EAEA,MAAM,cAAc,SAA0B,UAA+B;AAC3E,UAAM,KAAK,UAAU,YAAY,cAC/B,CAAC,QAAO;AACN,eAAS,GAAG;IACd,GACA,CAAC,QAAO;AACN,eAAS,MAAM,GAAG;IACpB,GAAC,OAAA,OAAA,EAEC,oBAAoB,OACpB,SAAS,KACT,YAAY,GACZ,uBAAuB,IAAI,GACxB,OAAO,CAAA;AAId,WAAO,GAAG,EAAE;EACd;EAEA,MAAM,WAAW,SAAuB;AACtC,cAAU,YAAY,WAAW,SAAS,QAAQ,IAAI,EAAE,CAAC;EAC3D;EAEA,MAAM,mBAAgB;AACpB,QAAI,OAAO,cAAc,eAAe,CAAC,UAAU,aAAa;AAC9D,YAAM,KAAK,YAAY,+CAA+C;IACxE;AAEA,UAAM,aAAa,MAAM,UAAU,YAAY,MAAM;MACnD,MAAM;KACP;AACD,WAAO,EAAE,UAAU,WAAW,OAAO,gBAAgB,WAAW,MAAK;EACvE;EAEA,MAAM,qBAAkB;AACtB,UAAM,KAAK,cAAc,yBAAyB;EACpD;;AAGF,IAAM,cAAc,IAAI,eAAc;", "names": []}