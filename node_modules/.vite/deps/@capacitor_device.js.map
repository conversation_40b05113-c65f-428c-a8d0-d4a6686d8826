{"version": 3, "sources": ["../../@capacitor/device/src/index.ts"], "sourcesContent": ["import { registerPlugin } from '@capacitor/core';\n\nimport type { DevicePlugin } from './definitions';\n\nconst Device = registerPlugin<DevicePlugin>('Device', {\n  web: () => import('./web').then(m => new m.DeviceWeb()),\n});\n\nexport * from './definitions';\nexport { Device };\n"], "mappings": ";;;;;;AAIA,IAAM,SAAS,eAA6B,UAAU;EACpD,KAAK,MAAM,OAAO,mBAAO,EAAE,KAAK,OAAK,IAAI,EAAE,UAAS,CAAE;CACvD;", "names": []}