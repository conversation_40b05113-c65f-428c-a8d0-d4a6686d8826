{"version": 3, "sources": ["../../@capacitor/push-notifications/src/index.ts"], "sourcesContent": ["import { registerPlugin } from '@capacitor/core';\n\nimport type { PushNotificationsPlugin } from './definitions';\n\nconst PushNotifications = registerPlugin<PushNotificationsPlugin>(\n  'PushNotifications',\n  {},\n);\n\nexport * from './definitions';\nexport { PushNotifications };\n"], "mappings": ";;;;;;AAIA,IAAM,oBAAoB,eACxB,qBACA,CAAA,CAAE;", "names": []}