{"version": 3, "sources": ["../../@capacitor/network/src/index.ts"], "sourcesContent": ["import { registerPlugin } from '@capacitor/core';\n\nimport type { NetworkPlugin } from './definitions';\n\nconst Network = registerPlugin<NetworkPlugin>('Network', {\n  web: () => import('./web').then(m => new m.NetworkWeb()),\n});\n\nexport * from './definitions';\nexport { Network };\n"], "mappings": ";;;;;;AAIA,IAAM,UAAU,eAA8B,WAAW;EACvD,KAAK,MAAM,OAAO,mBAAO,EAAE,KAAK,OAAK,IAAI,EAAE,WAAU,CAAE;CACxD;", "names": []}