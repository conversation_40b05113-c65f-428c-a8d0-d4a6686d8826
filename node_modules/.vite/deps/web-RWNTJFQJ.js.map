{"version": 3, "sources": ["../../@capacitor/network/src/web.ts"], "sourcesContent": ["import { WebPlugin } from '@capacitor/core';\n\nimport type {\n  ConnectionStatus,\n  ConnectionType,\n  NetworkPlugin,\n} from './definitions';\n\ndeclare global {\n  interface Navigator {\n    connection: any;\n    mozConnection: any;\n    webkitConnection: any;\n  }\n}\n\nfunction translatedConnection(): ConnectionType {\n  const connection =\n    window.navigator.connection ||\n    window.navigator.mozConnection ||\n    window.navigator.webkitConnection;\n  let result: ConnectionType = 'unknown';\n  const type = connection ? connection.type || connection.effectiveType : null;\n  if (type && typeof type === 'string') {\n    switch (type) {\n      // possible type values\n      case 'bluetooth':\n      case 'cellular':\n        result = 'cellular';\n        break;\n      case 'none':\n        result = 'none';\n        break;\n      case 'ethernet':\n      case 'wifi':\n      case 'wimax':\n        result = 'wifi';\n        break;\n      case 'other':\n      case 'unknown':\n        result = 'unknown';\n        break;\n      // possible effectiveType values\n      case 'slow-2g':\n      case '2g':\n      case '3g':\n        result = 'cellular';\n        break;\n      case '4g':\n        result = 'wifi';\n        break;\n      default:\n        break;\n    }\n  }\n  return result;\n}\n\nexport class NetworkWeb extends WebPlugin implements NetworkPlugin {\n  constructor() {\n    super();\n    if (typeof window !== 'undefined') {\n      window.addEventListener('online', this.handleOnline);\n      window.addEventListener('offline', this.handleOffline);\n    }\n  }\n\n  async getStatus(): Promise<ConnectionStatus> {\n    if (!window.navigator) {\n      throw this.unavailable(\n        'Browser does not support the Network Information API',\n      );\n    }\n\n    const connected = window.navigator.onLine;\n    const connectionType = translatedConnection();\n\n    const status: ConnectionStatus = {\n      connected,\n      connectionType: connected ? connectionType : 'none',\n    };\n\n    return status;\n  }\n\n  private handleOnline = () => {\n    const connectionType = translatedConnection();\n\n    const status: ConnectionStatus = {\n      connected: true,\n      connectionType: connectionType,\n    };\n\n    this.notifyListeners('networkStatusChange', status);\n  };\n\n  private handleOffline = () => {\n    const status: ConnectionStatus = {\n      connected: false,\n      connectionType: 'none',\n    };\n\n    this.notifyListeners('networkStatusChange', status);\n  };\n}\n\nconst Network = new NetworkWeb();\n\nexport { Network };\n"], "mappings": ";;;;;;AAgBA,SAAS,uBAAoB;AAC3B,QAAM,aACJ,OAAO,UAAU,cACjB,OAAO,UAAU,iBACjB,OAAO,UAAU;AACnB,MAAI,SAAyB;AAC7B,QAAM,OAAO,aAAa,WAAW,QAAQ,WAAW,gBAAgB;AACxE,MAAI,QAAQ,OAAO,SAAS,UAAU;AACpC,YAAQ,MAAM;;MAEZ,KAAK;MACL,KAAK;AACH,iBAAS;AACT;MACF,KAAK;AACH,iBAAS;AACT;MACF,KAAK;MACL,KAAK;MACL,KAAK;AACH,iBAAS;AACT;MACF,KAAK;MACL,KAAK;AACH,iBAAS;AACT;;MAEF,KAAK;MACL,KAAK;MACL,KAAK;AACH,iBAAS;AACT;MACF,KAAK;AACH,iBAAS;AACT;MACF;AACE;;;AAGN,SAAO;AACT;AAEM,IAAO,aAAP,cAA0B,UAAS;EACvC,cAAA;AACE,UAAK;AAyBC,SAAA,eAAe,MAAK;AAC1B,YAAM,iBAAiB,qBAAoB;AAE3C,YAAM,SAA2B;QAC/B,WAAW;QACX;;AAGF,WAAK,gBAAgB,uBAAuB,MAAM;IACpD;AAEQ,SAAA,gBAAgB,MAAK;AAC3B,YAAM,SAA2B;QAC/B,WAAW;QACX,gBAAgB;;AAGlB,WAAK,gBAAgB,uBAAuB,MAAM;IACpD;AA1CE,QAAI,OAAO,WAAW,aAAa;AACjC,aAAO,iBAAiB,UAAU,KAAK,YAAY;AACnD,aAAO,iBAAiB,WAAW,KAAK,aAAa;;EAEzD;EAEA,MAAM,YAAS;AACb,QAAI,CAAC,OAAO,WAAW;AACrB,YAAM,KAAK,YACT,sDAAsD;;AAI1D,UAAM,YAAY,OAAO,UAAU;AACnC,UAAM,iBAAiB,qBAAoB;AAE3C,UAAM,SAA2B;MAC/B;MACA,gBAAgB,YAAY,iBAAiB;;AAG/C,WAAO;EACT;;AAuBF,IAAM,UAAU,IAAI,WAAU;", "names": []}