{"version": 3, "sources": ["../../@capacitor/local-notifications/src/definitions.ts", "../../@capacitor/local-notifications/src/index.ts"], "sourcesContent": ["/// <reference types=\"@capacitor/cli\" />\n\nimport type { PermissionState, PluginListenerHandle } from '@capacitor/core';\n\ndeclare module '@capacitor/cli' {\n  export interface PluginsConfig {\n    /**\n     * On Android, the Local Notifications can be configured with the following options:\n     */\n    LocalNotifications?: {\n      /**\n       * Set the default status bar icon for notifications.\n       *\n       * Icons should be placed in your app's `res/drawable` folder. The value for\n       * this option should be the drawable resource ID, which is the filename\n       * without an extension.\n       *\n       * Only available for Android.\n       *\n       * @since 1.0.0\n       * @example \"ic_stat_icon_config_sample\"\n       */\n      smallIcon?: string;\n\n      /**\n       * Set the default color of status bar icons for notifications.\n       *\n       * Only available for Android.\n       *\n       * @since 1.0.0\n       * @example \"#488AFF\"\n       */\n      iconColor?: string;\n\n      /**\n       * Set the default notification sound for notifications.\n       *\n       * On Android 26+ it sets the default channel sound and can't be\n       * changed unless the app is uninstalled.\n       *\n       * If the audio file is not found, it will result in the default system\n       * sound being played on Android 21-25 and no sound on Android 26+.\n       *\n       * Only available for Android.\n       *\n       * @since 1.0.0\n       * @example \"beep.wav\"\n       */\n      sound?: string;\n    };\n  }\n}\n\nexport interface LocalNotificationsPlugin {\n  /**\n   * Schedule one or more local notifications.\n   *\n   * @since 1.0.0\n   */\n  schedule(options: ScheduleOptions): Promise<ScheduleResult>;\n\n  /**\n   * Get a list of pending notifications.\n   *\n   * @since 1.0.0\n   */\n  getPending(): Promise<PendingResult>;\n\n  /**\n   * Register actions to take when notifications are displayed.\n   *\n   * Only available for iOS and Android.\n   *\n   * @since 1.0.0\n   */\n  registerActionTypes(options: RegisterActionTypesOptions): Promise<void>;\n\n  /**\n   * Cancel pending notifications.\n   *\n   * @since 1.0.0\n   */\n  cancel(options: CancelOptions): Promise<void>;\n\n  /**\n   * Check if notifications are enabled or not.\n   *\n   * @deprecated Use `checkPermissions()` to check if the user has allowed\n   * notifications to be displayed.\n   * @since 1.0.0\n   */\n  areEnabled(): Promise<EnabledResult>;\n\n  /**\n   * Get a list of notifications that are visible on the notifications screen.\n   *\n   * @since 4.0.0\n   */\n  getDeliveredNotifications(): Promise<DeliveredNotifications>;\n\n  /**\n   * Remove the specified notifications from the notifications screen.\n   *\n   * @since 4.0.0\n   */\n  removeDeliveredNotifications(\n    delivered: DeliveredNotifications,\n  ): Promise<void>;\n\n  /**\n   * Remove all the notifications from the notifications screen.\n   *\n   * @since 4.0.0\n   */\n  removeAllDeliveredNotifications(): Promise<void>;\n\n  /**\n   * Create a notification channel.\n   *\n   * Only available for Android.\n   *\n   * @since 1.0.0\n   */\n  createChannel(channel: Channel): Promise<void>;\n\n  /**\n   * Delete a notification channel.\n   *\n   * Only available for Android.\n   *\n   * @since 1.0.0\n   */\n  deleteChannel(args: { id: string }): Promise<void>;\n\n  /**\n   * Get a list of notification channels.\n   *\n   * Only available for Android.\n   *\n   * @since 1.0.0\n   */\n  listChannels(): Promise<ListChannelsResult>;\n\n  /**\n   * Check permission to display local notifications.\n   *\n   * @since 1.0.0\n   */\n  checkPermissions(): Promise<PermissionStatus>;\n\n  /**\n   * Request permission to display local notifications.\n   *\n   * @since 1.0.0\n   */\n  requestPermissions(): Promise<PermissionStatus>;\n\n  /**\n   * Direct user to the application settings screen to configure exact alarms.\n   *\n   * In the event that a user changes the settings from granted to denied, the application\n   * will restart and any notification scheduled with an exact alarm will be deleted.\n   *\n   * On Android < 12, the user will NOT be directed to the application settings screen, instead this function will\n   * return `granted`.\n   *\n   * Only available on Android.\n   *\n   * @since 6.0.0\n   */\n  changeExactNotificationSetting(): Promise<SettingsPermissionStatus>;\n\n  /**\n   * Check application setting for using exact alarms.\n   *\n   * Only available on Android.\n   *\n   * @since 6.0.0\n   */\n  checkExactNotificationSetting(): Promise<SettingsPermissionStatus>;\n\n  /**\n   * Listen for when notifications are displayed.\n   *\n   * @since 1.0.0\n   */\n  addListener(\n    eventName: 'localNotificationReceived',\n    listenerFunc: (notification: LocalNotificationSchema) => void,\n  ): Promise<PluginListenerHandle>;\n\n  /**\n   * Listen for when an action is performed on a notification.\n   *\n   * @since 1.0.0\n   */\n  addListener(\n    eventName: 'localNotificationActionPerformed',\n    listenerFunc: (notificationAction: ActionPerformed) => void,\n  ): Promise<PluginListenerHandle>;\n\n  /**\n   * Remove all listeners for this plugin.\n   *\n   * @since 1.0.0\n   */\n  removeAllListeners(): Promise<void>;\n}\n\n/**\n * The object that describes a local notification.\n *\n * @since 1.0.0\n */\nexport interface LocalNotificationDescriptor {\n  /**\n   * The notification identifier.\n   *\n   * @since 1.0.0\n   */\n  id: number;\n}\n\nexport interface ScheduleOptions {\n  /**\n   * The list of notifications to schedule.\n   *\n   * @since 1.0.0\n   */\n  notifications: LocalNotificationSchema[];\n}\n\nexport interface ScheduleResult {\n  /**\n   * The list of scheduled notifications.\n   *\n   * @since 1.0.0\n   */\n  notifications: LocalNotificationDescriptor[];\n}\n\nexport interface PendingResult {\n  /**\n   * The list of pending notifications.\n   *\n   * @since 1.0.0\n   */\n  notifications: PendingLocalNotificationSchema[];\n}\n\nexport interface RegisterActionTypesOptions {\n  /**\n   * The list of action types to register.\n   *\n   * @since 1.0.0\n   */\n  types: ActionType[];\n}\n\nexport interface CancelOptions {\n  /**\n   * The list of notifications to cancel.\n   *\n   * @since 1.0.0\n   */\n  notifications: LocalNotificationDescriptor[];\n}\n\n/**\n * A collection of actions.\n *\n * @since 1.0.0\n */\nexport interface ActionType {\n  /**\n   * The ID of the action type.\n   *\n   * Referenced in notifications by the `actionTypeId` key.\n   *\n   * @since 1.0.0\n   */\n  id: string;\n\n  /**\n   * The list of actions associated with this action type.\n   *\n   * @since 1.0.0\n   */\n  actions?: Action[];\n\n  /**\n   * Sets `hiddenPreviewsBodyPlaceholder` of the\n   * [`UNNotificationCategory`](https://developer.apple.com/documentation/usernotifications/unnotificationcategory).\n   *\n   * Only available for iOS.\n   *\n   * @since 1.0.0\n   */\n  iosHiddenPreviewsBodyPlaceholder?: string;\n\n  /**\n   * Sets `customDismissAction` in the options of the\n   * [`UNNotificationCategory`](https://developer.apple.com/documentation/usernotifications/unnotificationcategory).\n   *\n   * Only available for iOS.\n   *\n   * @since 1.0.0\n   */\n  iosCustomDismissAction?: boolean;\n\n  /**\n   * Sets `allowInCarPlay` in the options of the\n   * [`UNNotificationCategory`](https://developer.apple.com/documentation/usernotifications/unnotificationcategory).\n   *\n   * Only available for iOS.\n   *\n   * @since 1.0.0\n   */\n  iosAllowInCarPlay?: boolean;\n\n  /**\n   * Sets `hiddenPreviewsShowTitle` in the options of the\n   * [`UNNotificationCategory`](https://developer.apple.com/documentation/usernotifications/unnotificationcategory).\n   *\n   * Only available for iOS.\n   *\n   * @since 1.0.0\n   */\n  iosHiddenPreviewsShowTitle?: boolean;\n\n  /**\n   * Sets `hiddenPreviewsShowSubtitle` in the options of the\n   * [`UNNotificationCategory`](https://developer.apple.com/documentation/usernotifications/unnotificationcategory).\n   *\n   * Only available for iOS.\n   *\n   * @since 1.0.0\n   */\n  iosHiddenPreviewsShowSubtitle?: boolean;\n}\n\n/**\n * An action that can be taken when a notification is displayed.\n *\n * @since 1.0.0\n */\nexport interface Action {\n  /**\n   * The action identifier.\n   *\n   * Referenced in the `'actionPerformed'` event as `actionId`.\n   *\n   * @since 1.0.0\n   */\n  id: string;\n\n  /**\n   * The title text to display for this action.\n   *\n   * @since 1.0.0\n   */\n  title: string;\n\n  /**\n   * Sets `authenticationRequired` in the options of the\n   * [`UNNotificationAction`](https://developer.apple.com/documentation/usernotifications/unnotificationaction).\n   *\n   * Only available for iOS.\n   *\n   * @since 1.0.0\n   */\n  requiresAuthentication?: boolean;\n\n  /**\n   * Sets `foreground` in the options of the\n   * [`UNNotificationAction`](https://developer.apple.com/documentation/usernotifications/unnotificationaction).\n   *\n   * Only available for iOS.\n   *\n   * @since 1.0.0\n   */\n  foreground?: boolean;\n\n  /**\n   * Sets `destructive` in the options of the\n   * [`UNNotificationAction`](https://developer.apple.com/documentation/usernotifications/unnotificationaction).\n   *\n   * Only available for iOS.\n   *\n   * @since 1.0.0\n   */\n  destructive?: boolean;\n\n  /**\n   * Use a `UNTextInputNotificationAction` instead of a `UNNotificationAction`.\n   *\n   * Only available for iOS.\n   *\n   * @since 1.0.0\n   */\n  input?: boolean;\n\n  /**\n   * Sets `textInputButtonTitle` on the\n   * [`UNTextInputNotificationAction`](https://developer.apple.com/documentation/usernotifications/untextinputnotificationaction).\n   *\n   * Only available for iOS when `input` is `true`.\n   *\n   * @since 1.0.0\n   */\n  inputButtonTitle?: string;\n\n  /**\n   * Sets `textInputPlaceholder` on the\n   * [`UNTextInputNotificationAction`](https://developer.apple.com/documentation/usernotifications/untextinputnotificationaction).\n   *\n   * Only available for iOS when `input` is `true`.\n   *\n   * @since 1.0.0\n   */\n  inputPlaceholder?: string;\n}\n\n/**\n * Represents a notification attachment.\n *\n * @since 1.0.0\n */\nexport interface Attachment {\n  /**\n   * The attachment identifier.\n   *\n   * @since 1.0.0\n   */\n  id: string;\n\n  /**\n   * The URL to the attachment.\n   *\n   * Use the `res` scheme to reference web assets, e.g.\n   * `res:///assets/img/icon.png`. Also accepts `file` URLs.\n   *\n   * @since 1.0.0\n   */\n  url: string;\n\n  /**\n   * Attachment options.\n   *\n   * @since 1.0.0\n   */\n  options?: AttachmentOptions;\n}\n\nexport interface AttachmentOptions {\n  /**\n   * Sets the `UNNotificationAttachmentOptionsTypeHintKey` key in the hashable\n   * options of\n   * [`UNNotificationAttachment`](https://developer.apple.com/documentation/usernotifications/unnotificationattachment).\n   *\n   * Only available for iOS.\n   *\n   * @since 1.0.0\n   */\n  iosUNNotificationAttachmentOptionsTypeHintKey?: string;\n\n  /**\n   * Sets the `UNNotificationAttachmentOptionsThumbnailHiddenKey` key in the\n   * hashable options of\n   * [`UNNotificationAttachment`](https://developer.apple.com/documentation/usernotifications/unnotificationattachment).\n   *\n   * Only available for iOS.\n   *\n   * @since 1.0.0\n   */\n  iosUNNotificationAttachmentOptionsThumbnailHiddenKey?: string;\n\n  /**\n   * Sets the `UNNotificationAttachmentOptionsThumbnailClippingRectKey` key in\n   * the hashable options of\n   * [`UNNotificationAttachment`](https://developer.apple.com/documentation/usernotifications/unnotificationattachment).\n   *\n   * Only available for iOS.\n   *\n   * @since 1.0.0\n   */\n  iosUNNotificationAttachmentOptionsThumbnailClippingRectKey?: string;\n\n  /**\n   * Sets the `UNNotificationAttachmentOptionsThumbnailTimeKey` key in the\n   * hashable options of\n   * [`UNNotificationAttachment`](https://developer.apple.com/documentation/usernotifications/unnotificationattachment).\n   *\n   * Only available for iOS.\n   *\n   * @since 1.0.0\n   */\n  iosUNNotificationAttachmentOptionsThumbnailTimeKey?: string;\n}\n\nexport interface PendingLocalNotificationSchema {\n  /**\n   * The title of the notification.\n   *\n   * @since 1.0.0\n   */\n  title: string;\n\n  /**\n   * The body of the notification, shown below the title.\n   *\n   * @since 1.0.0\n   */\n  body: string;\n\n  /**\n   * The notification identifier.\n   *\n   * @since 1.0.0\n   */\n  id: number;\n\n  /**\n   * Schedule this notification for a later time.\n   *\n   * @since 1.0.0\n   */\n  schedule?: Schedule;\n\n  /**\n   * Set extra data to store within this notification.\n   *\n   * @since 1.0.0\n   */\n  extra?: any;\n}\n\nexport interface LocalNotificationSchema {\n  /**\n   * The title of the notification.\n   *\n   * @since 1.0.0\n   */\n  title: string;\n\n  /**\n   * The body of the notification, shown below the title.\n   *\n   * @since 1.0.0\n   */\n  body: string;\n\n  /**\n   * Sets a multiline text block for display in a big text notification style.\n   *\n   * @since 1.0.0\n   */\n  largeBody?: string;\n\n  /**\n   * Used to set the summary text detail in inbox and big text notification styles.\n   *\n   * Only available for Android.\n   *\n   * @since 1.0.0\n   */\n  summaryText?: string;\n  /**\n   * The notification identifier.\n   * On Android it's a 32-bit int.\n   * So the value should be between -2147483648 and 2147483647 inclusive.\n   *\n   * @since 1.0.0\n   */\n  id: number;\n\n  /**\n   * Schedule this notification for a later time.\n   *\n   * @since 1.0.0\n   */\n  schedule?: Schedule;\n\n  /**\n   * Name of the audio file to play when this notification is displayed.\n   *\n   * Include the file extension with the filename.\n   *\n   * On iOS, the file should be in the app bundle.\n   * On Android, the file should be in res/raw folder.\n   *\n   * Recommended format is `.wav` because is supported by both iOS and Android.\n   *\n   * Only available for iOS and Android < 26.\n   * For Android 26+ use channelId of a channel configured with the desired sound.\n   *\n   * If the sound file is not found, (i.e. empty string or wrong name)\n   * the default system notification sound will be used.\n   * If not provided, it will produce the default sound on Android and no sound on iOS.\n   *\n   * @since 1.0.0\n   */\n  sound?: string;\n\n  /**\n   * Set a custom status bar icon.\n   *\n   * If set, this overrides the `smallIcon` option from Capacitor\n   * configuration.\n   *\n   * Icons should be placed in your app's `res/drawable` folder. The value for\n   * this option should be the drawable resource ID, which is the filename\n   * without an extension.\n   *\n   * Only available for Android.\n   *\n   * @since 1.0.0\n   */\n  smallIcon?: string;\n\n  /**\n   * Set a large icon for notifications.\n   *\n   * Icons should be placed in your app's `res/drawable` folder. The value for\n   * this option should be the drawable resource ID, which is the filename\n   * without an extension.\n   *\n   * Only available for Android.\n   *\n   * @since 1.0.0\n   */\n  largeIcon?: string;\n\n  /**\n   * Set the color of the notification icon.\n   *\n   * Only available for Android.\n   *\n   * @since 1.0.0\n   */\n  iconColor?: string;\n\n  /**\n   * Set attachments for this notification.\n   *\n   * @since 1.0.0\n   */\n  attachments?: Attachment[];\n\n  /**\n   * Associate an action type with this notification.\n   *\n   * @since 1.0.0\n   */\n  actionTypeId?: string;\n\n  /**\n   * Set extra data to store within this notification.\n   *\n   * @since 1.0.0\n   */\n  extra?: any;\n\n  /**\n   * Used to group multiple notifications.\n   *\n   * Sets `threadIdentifier` on the\n   * [`UNMutableNotificationContent`](https://developer.apple.com/documentation/usernotifications/unmutablenotificationcontent).\n   *\n   * Only available for iOS.\n   *\n   * @since 1.0.0\n   */\n  threadIdentifier?: string;\n\n  /**\n   * The string this notification adds to the category's summary format string.\n   *\n   * Sets `summaryArgument` on the\n   * [`UNMutableNotificationContent`](https://developer.apple.com/documentation/usernotifications/unmutablenotificationcontent).\n   *\n   * Only available for iOS.\n   *\n   * @since 1.0.0\n   */\n  summaryArgument?: string;\n\n  /**\n   * Used to group multiple notifications.\n   *\n   * Calls `setGroup()` on\n   * [`NotificationCompat.Builder`](https://developer.android.com/reference/androidx/core/app/NotificationCompat.Builder)\n   * with the provided value.\n   *\n   * Only available for Android.\n   *\n   * @since 1.0.0\n   */\n  group?: string;\n\n  /**\n   * If true, this notification becomes the summary for a group of\n   * notifications.\n   *\n   * Calls `setGroupSummary()` on\n   * [`NotificationCompat.Builder`](https://developer.android.com/reference/androidx/core/app/NotificationCompat.Builder)\n   * with the provided value.\n   *\n   * Only available for Android when using `group`.\n   *\n   * @since 1.0.0\n   */\n  groupSummary?: boolean;\n\n  /**\n   * Specifies the channel the notification should be delivered on.\n   *\n   * If channel with the given name does not exist then the notification will\n   * not fire. If not provided, it will use the default channel.\n   *\n   * Calls `setChannelId()` on\n   * [`NotificationCompat.Builder`](https://developer.android.com/reference/androidx/core/app/NotificationCompat.Builder)\n   * with the provided value.\n   *\n   * Only available for Android 26+.\n   *\n   * @since 1.0.0\n   */\n  channelId?: string;\n\n  /**\n   * If true, the notification can't be swiped away.\n   *\n   * Calls `setOngoing()` on\n   * [`NotificationCompat.Builder`](https://developer.android.com/reference/androidx/core/app/NotificationCompat.Builder)\n   * with the provided value.\n   *\n   * Only available for Android.\n   *\n   * @since 1.0.0\n   */\n  ongoing?: boolean;\n\n  /**\n   * If true, the notification is canceled when the user clicks on it.\n   *\n   * Calls `setAutoCancel()` on\n   * [`NotificationCompat.Builder`](https://developer.android.com/reference/androidx/core/app/NotificationCompat.Builder)\n   * with the provided value.\n   *\n   * Only available for Android.\n   *\n   * @since 1.0.0\n   */\n  autoCancel?: boolean;\n\n  /**\n   * Sets a list of strings for display in an inbox style notification.\n   *\n   * Up to 5 strings are allowed.\n   *\n   * Only available for Android.\n   *\n   * @since 1.0.0\n   */\n  inboxList?: string[];\n\n  /**\n   * If true, notification will not appear while app is in the foreground.\n   *\n   * Only available for iOS.\n   *\n   * @since 5.0.0\n   */\n  silent?: boolean;\n}\n\n/**\n * Represents a schedule for a notification.\n *\n * Use either `at`, `on`, or `every` to schedule notifications.\n *\n * @since 1.0.0\n */\nexport interface Schedule {\n  /**\n   * Schedule a notification at a specific date and time.\n   *\n   * @since 1.0.0\n   */\n  at?: Date;\n\n  /**\n   * Repeat delivery of this notification at the date and time specified by\n   * `at`.\n   *\n   * Only available for iOS and Android.\n   *\n   * @since 1.0.0\n   */\n  repeats?: boolean;\n\n  /**\n   * Allow this notification to fire while in [Doze](https://developer.android.com/training/monitoring-device-state/doze-standby)\n   *\n   * Only available for Android 23+.\n   *\n   * Note that these notifications can only fire [once per 9 minutes, per app](https://developer.android.com/training/monitoring-device-state/doze-standby#assessing_your_app).\n   *\n   * @since 1.0.0\n   */\n  allowWhileIdle?: boolean;\n\n  /**\n   * Schedule a notification on particular interval(s).\n   *\n   * This is similar to scheduling [cron](https://en.wikipedia.org/wiki/Cron)\n   * jobs.\n   *\n   * Only available for iOS and Android.\n   *\n   * @since 1.0.0\n   */\n  on?: ScheduleOn;\n\n  /**\n   * Schedule a notification on a particular interval.\n   *\n   * @since 1.0.0\n   */\n  every?: ScheduleEvery;\n\n  /**\n   * Limit the number times a notification is delivered by the interval\n   * specified by `every`.\n   *\n   * @since 1.0.0\n   */\n  count?: number;\n}\n\nexport interface ScheduleOn {\n  year?: number;\n  month?: number;\n  day?: number;\n  weekday?: Weekday;\n  hour?: number;\n  minute?: number;\n  second?: number;\n}\n\nexport type ScheduleEvery =\n  | 'year'\n  | 'month'\n  | 'two-weeks'\n  | 'week'\n  | 'day'\n  | 'hour'\n  | 'minute'\n  | 'second';\n\nexport interface ListChannelsResult {\n  /**\n   * The list of notification channels.\n   *\n   * @since 1.0.0\n   */\n  channels: Channel[];\n}\n\nexport interface PermissionStatus {\n  /**\n   * Permission state of displaying notifications.\n   *\n   * @since 1.0.0\n   */\n  display: PermissionState;\n}\n\nexport interface SettingsPermissionStatus {\n  /**\n   * Permission state of using exact alarms.\n   *\n   * @since 6.0.0\n   */\n  exact_alarm: PermissionState;\n}\n\nexport interface ActionPerformed {\n  /**\n   * The identifier of the performed action.\n   *\n   * @since 1.0.0\n   */\n  actionId: string;\n\n  /**\n   * The value entered by the user on the notification.\n   *\n   * Only available on iOS for notifications with `input` set to `true`.\n   *\n   * @since 1.0.0\n   */\n  inputValue?: string;\n\n  /**\n   * The original notification schema.\n   *\n   * @since 1.0.0\n   */\n  notification: LocalNotificationSchema;\n}\n\n/**\n * @deprecated\n */\nexport interface EnabledResult {\n  /**\n   * Whether or not the device has local notifications enabled.\n   *\n   * @since 1.0.0\n   */\n  value: boolean;\n}\n\nexport interface DeliveredNotificationSchema {\n  /**\n   * The notification identifier.\n   *\n   * @since 4.0.0\n   */\n  id: number;\n\n  /**\n   * The notification tag.\n   *\n   * Only available on Android.\n   *\n   * @since 4.0.0\n   */\n  tag?: string;\n  /**\n   * The title of the notification.\n   *\n   * @since 4.0.0\n   */\n  title: string;\n\n  /**\n   * The body of the notification, shown below the title.\n   *\n   * @since 4.0.0\n   */\n  body: string;\n\n  /**\n   * The configured group of the notification.\n   *\n   *\n   * Only available for Android.\n   *\n   * @since 4.0.0\n   */\n  group?: string;\n\n  /**\n   * If this notification is the summary for a group of notifications.\n   *\n   * Only available for Android.\n   *\n   * @since 4.0.0\n   */\n  groupSummary?: boolean;\n\n  /**\n   * Any additional data that was included in the\n   * notification payload.\n   *\n   * Only available for Android.\n   *\n   * @since 4.0.0\n   */\n  data?: any;\n\n  /**\n   * Extra data to store within this notification.\n   *\n   * Only available for iOS.\n   *\n   * @since 4.0.0\n   */\n  extra?: any;\n\n  /**\n   * The attachments for this notification.\n   *\n   * Only available for iOS.\n   *\n   * @since 1.0.0\n   */\n  attachments?: Attachment[];\n\n  /**\n   * Action type ssociated with this notification.\n   *\n   * Only available for iOS.\n   *\n   * @since 4.0.0\n   */\n  actionTypeId?: string;\n\n  /**\n   * Schedule used to fire this notification.\n   *\n   * Only available for iOS.\n   *\n   * @since 4.0.0\n   */\n  schedule?: Schedule;\n\n  /**\n   * Sound that was used when the notification was displayed.\n   *\n   * Only available for iOS.\n   *\n   * @since 4.0.0\n   */\n  sound?: string;\n}\n\nexport interface DeliveredNotifications {\n  /**\n   * List of notifications that are visible on the\n   * notifications screen.\n   *\n   * @since 1.0.0\n   */\n  notifications: DeliveredNotificationSchema[];\n}\n\nexport interface Channel {\n  /**\n   * The channel identifier.\n   *\n   * @since 1.0.0\n   */\n  id: string;\n\n  /**\n   * The human-friendly name of this channel (presented to the user).\n   *\n   * @since 1.0.0\n   */\n  name: string;\n\n  /**\n   * The description of this channel (presented to the user).\n   *\n   * @since 1.0.0\n   */\n  description?: string;\n\n  /**\n   * The sound that should be played for notifications posted to this channel.\n   *\n   * Notification channels with an importance of at least `3` should have a\n   * sound.\n   *\n   * The file name of a sound file should be specified relative to the android\n   * app `res/raw` directory.\n   *\n   * If the sound is not provided, or the sound file is not found no sound will be used.\n   *\n   * @since 1.0.0\n   * @example \"jingle.wav\"\n   */\n  sound?: string;\n\n  /**\n   * The level of interruption for notifications posted to this channel.\n   *\n   * @default `3`\n   * @since 1.0.0\n   */\n  importance?: Importance;\n\n  /**\n   * The visibility of notifications posted to this channel.\n   *\n   * This setting is for whether notifications posted to this channel appear on\n   * the lockscreen or not, and if so, whether they appear in a redacted form.\n   *\n   * @since 1.0.0\n   */\n  visibility?: Visibility;\n\n  /**\n   * Whether notifications posted to this channel should display notification\n   * lights, on devices that support it.\n   *\n   * @since 1.0.0\n   */\n  lights?: boolean;\n\n  /**\n   * The light color for notifications posted to this channel.\n   *\n   * Only supported if lights are enabled on this channel and the device\n   * supports it.\n   *\n   * Supported color formats are `#RRGGBB` and `#RRGGBBAA`.\n   *\n   * @since 1.0.0\n   */\n  lightColor?: string;\n\n  /**\n   * Whether notifications posted to this channel should vibrate.\n   *\n   * @since 1.0.0\n   */\n  vibration?: boolean;\n}\n\n/**\n * Day of the week. Used for scheduling notifications on a particular weekday.\n */\nexport enum Weekday {\n  Sunday = 1,\n  Monday = 2,\n  Tuesday = 3,\n  Wednesday = 4,\n  Thursday = 5,\n  Friday = 6,\n  Saturday = 7,\n}\n\n/**\n * The importance level. For more details, see the [Android Developer Docs](https://developer.android.com/reference/android/app/NotificationManager#IMPORTANCE_DEFAULT)\n * @since 1.0.0\n */\nexport type Importance = 1 | 2 | 3 | 4 | 5;\n\n/**\n * The notification visibility. For more details, see the [Android Developer Docs](https://developer.android.com/reference/androidx/core/app/NotificationCompat#VISIBILITY_PRIVATE)\n * @since 1.0.0\n */\nexport type Visibility = -1 | 0 | 1;\n\n/**\n * @deprecated Use 'Channel`.\n * @since 1.0.0\n */\nexport type NotificationChannel = Channel;\n\n/**\n * @deprecated Use `LocalNotificationDescriptor`.\n * @since 1.0.0\n */\nexport type LocalNotificationRequest = LocalNotificationDescriptor;\n\n/**\n * @deprecated Use `ScheduleResult`.\n * @since 1.0.0\n */\nexport type LocalNotificationScheduleResult = ScheduleResult;\n\n/**\n * @deprecated Use `PendingResult`.\n * @since 1.0.0\n */\nexport type LocalNotificationPendingList = PendingResult;\n\n/**\n * @deprecated Use `ActionType`.\n * @since 1.0.0\n */\nexport type LocalNotificationActionType = ActionType;\n\n/**\n * @deprecated Use `Action`.\n * @since 1.0.0\n */\nexport type LocalNotificationAction = Action;\n\n/**\n * @deprecated Use `EnabledResult`.\n * @since 1.0.0\n */\nexport type LocalNotificationEnabledResult = EnabledResult;\n\n/**\n * @deprecated Use `ListChannelsResult`.\n * @since 1.0.0\n */\nexport type NotificationChannelList = ListChannelsResult;\n\n/**\n * @deprecated Use `Attachment`.\n * @since 1.0.0\n */\nexport type LocalNotificationAttachment = Attachment;\n\n/**\n * @deprecated Use `AttachmentOptions`.\n * @since 1.0.0\n */\nexport type LocalNotificationAttachmentOptions = AttachmentOptions;\n\n/**\n * @deprecated Use `LocalNotificationSchema`.\n * @since 1.0.0\n */\nexport type LocalNotification = LocalNotificationSchema;\n\n/**\n * @deprecated Use `Schedule`.\n * @since 1.0.0\n */\nexport type LocalNotificationSchedule = Schedule;\n\n/**\n * @deprecated Use `ActionPerformed`.\n * @since 1.0.0\n */\nexport type LocalNotificationActionPerformed = ActionPerformed;\n", "import { registerPlugin } from '@capacitor/core';\n\nimport type { LocalNotificationsPlugin } from './definitions';\n\nconst LocalNotifications = registerPlugin<LocalNotificationsPlugin>(\n  'LocalNotifications',\n  {\n    web: () => import('./web').then(m => new m.LocalNotificationsWeb()),\n  },\n);\n\nexport * from './definitions';\nexport { LocalNotifications };\n"], "mappings": ";;;;;;AAumCA,IAAY;CAAZ,SAAYA,UAAO;AACjB,EAAAA,SAAAA,SAAA,QAAA,IAAA,CAAA,IAAA;AACA,EAAAA,SAAAA,SAAA,QAAA,IAAA,CAAA,IAAA;AACA,EAAAA,SAAAA,SAAA,SAAA,IAAA,CAAA,IAAA;AACA,EAAAA,SAAAA,SAAA,WAAA,IAAA,CAAA,IAAA;AACA,EAAAA,SAAAA,SAAA,UAAA,IAAA,CAAA,IAAA;AACA,EAAAA,SAAAA,SAAA,QAAA,IAAA,CAAA,IAAA;AACA,EAAAA,SAAAA,SAAA,UAAA,IAAA,CAAA,IAAA;AACF,GARY,YAAA,UAAO,CAAA,EAAA;;;ACnmCnB,IAAM,qBAAqB,eACzB,sBACA;EACE,KAAK,MAAM,OAAO,mBAAO,EAAE,KAAK,OAAK,IAAI,EAAE,sBAAqB,CAAE;CACnE;", "names": ["Weekday"]}