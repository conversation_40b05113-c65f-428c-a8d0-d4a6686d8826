{"version": 3, "sources": ["../../@capacitor/haptics/src/web.ts"], "sourcesContent": ["import { WebPlugin } from '@capacitor/core';\n\nimport { ImpactStyle, NotificationType } from './definitions';\nimport type {\n  HapticsPlugin,\n  ImpactOptions,\n  NotificationOptions,\n  VibrateOptions,\n} from './definitions';\n\nexport class HapticsWeb extends WebPlugin implements HapticsPlugin {\n  selectionStarted = false;\n\n  async impact(options?: ImpactOptions): Promise<void> {\n    const pattern = this.patternForImpact(options?.style);\n    this.vibrateWithPattern(pattern);\n  }\n\n  async notification(options?: NotificationOptions): Promise<void> {\n    const pattern = this.patternForNotification(options?.type);\n    this.vibrateWithPattern(pattern);\n  }\n\n  async vibrate(options?: VibrateOptions): Promise<void> {\n    const duration = options?.duration || 300;\n    this.vibrateWithPattern([duration]);\n  }\n\n  async selectionStart(): Promise<void> {\n    this.selectionStarted = true;\n  }\n\n  async selectionChanged(): Promise<void> {\n    if (this.selectionStarted) {\n      this.vibrateWithPattern([70]);\n    }\n  }\n\n  async selectionEnd(): Promise<void> {\n    this.selectionStarted = false;\n  }\n\n  private patternForImpact(style: ImpactStyle = ImpactStyle.Heavy): number[] {\n    if (style === ImpactStyle.Medium) {\n      return [43];\n    } else if (style === ImpactStyle.Light) {\n      return [20];\n    }\n    return [61];\n  }\n\n  private patternForNotification(\n    type: NotificationType = NotificationType.Success,\n  ): number[] {\n    if (type === NotificationType.Warning) {\n      return [30, 40, 30, 50, 60];\n    } else if (type === NotificationType.Error) {\n      return [27, 45, 50];\n    }\n    return [35, 65, 21];\n  }\n\n  private vibrateWithPattern(pattern: number[]) {\n    if (navigator.vibrate) {\n      navigator.vibrate(pattern);\n    } else {\n      throw this.unavailable('Browser does not support the vibrate API');\n    }\n  }\n}\n"], "mappings": ";;;;;;;;;;AAUM,IAAO,aAAP,cAA0B,UAAS;EAAzC,cAAA;;AACE,SAAA,mBAAmB;EA0DrB;EAxDE,MAAM,OAAO,SAAuB;AAClC,UAAM,UAAU,KAAK,iBAAiB,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,KAAK;AACpD,SAAK,mBAAmB,OAAO;EACjC;EAEA,MAAM,aAAa,SAA6B;AAC9C,UAAM,UAAU,KAAK,uBAAuB,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,IAAI;AACzD,SAAK,mBAAmB,OAAO;EACjC;EAEA,MAAM,QAAQ,SAAwB;AACpC,UAAM,YAAW,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,aAAY;AACtC,SAAK,mBAAmB,CAAC,QAAQ,CAAC;EACpC;EAEA,MAAM,iBAAc;AAClB,SAAK,mBAAmB;EAC1B;EAEA,MAAM,mBAAgB;AACpB,QAAI,KAAK,kBAAkB;AACzB,WAAK,mBAAmB,CAAC,EAAE,CAAC;;EAEhC;EAEA,MAAM,eAAY;AAChB,SAAK,mBAAmB;EAC1B;EAEQ,iBAAiB,QAAqB,YAAY,OAAK;AAC7D,QAAI,UAAU,YAAY,QAAQ;AAChC,aAAO,CAAC,EAAE;eACD,UAAU,YAAY,OAAO;AACtC,aAAO,CAAC,EAAE;;AAEZ,WAAO,CAAC,EAAE;EACZ;EAEQ,uBACN,OAAyB,iBAAiB,SAAO;AAEjD,QAAI,SAAS,iBAAiB,SAAS;AACrC,aAAO,CAAC,IAAI,IAAI,IAAI,IAAI,EAAE;eACjB,SAAS,iBAAiB,OAAO;AAC1C,aAAO,CAAC,IAAI,IAAI,EAAE;;AAEpB,WAAO,CAAC,IAAI,IAAI,EAAE;EACpB;EAEQ,mBAAmB,SAAiB;AAC1C,QAAI,UAAU,SAAS;AACrB,gBAAU,QAAQ,OAAO;WACpB;AACL,YAAM,KAAK,YAAY,0CAA0C;;EAErE;;", "names": []}