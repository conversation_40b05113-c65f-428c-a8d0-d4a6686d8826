{"version": 3, "sources": ["../../@capacitor/synapse/dist/synapse.mjs", "../../@capacitor/geolocation/src/index.ts"], "sourcesContent": ["function s(t) {\n  t.CapacitorUtils.Synapse = new Proxy(\n    {},\n    {\n      get(e, n) {\n        return new Proxy({}, {\n          get(w, o) {\n            return (c, p, r) => {\n              const i = t.Capacitor.Plugins[n];\n              if (i === void 0) {\n                r(new Error(`Capacitor plugin ${n} not found`));\n                return;\n              }\n              if (typeof i[o] != \"function\") {\n                r(new Error(`Method ${o} not found in Capacitor plugin ${n}`));\n                return;\n              }\n              (async () => {\n                try {\n                  const a = await i[o](c);\n                  p(a);\n                } catch (a) {\n                  r(a);\n                }\n              })();\n            };\n          }\n        });\n      }\n    }\n  );\n}\nfunction u(t) {\n  t.CapacitorUtils.Synapse = new Proxy(\n    {},\n    {\n      get(e, n) {\n        return t.cordova.plugins[n];\n      }\n    }\n  );\n}\nfunction f(t = !1) {\n  typeof window > \"u\" || (window.CapacitorUtils = window.CapacitorUtils || {}, window.Capacitor !== void 0 && !t ? s(window) : window.cordova !== void 0 && u(window));\n}\nexport {\n  f as exposeSynapse\n};\n", "import { registerPlugin } from '@capacitor/core';\nimport { exposeSynapse } from '@capacitor/synapse';\n\nimport type { GeolocationPlugin } from './definitions';\n\nconst Geolocation = registerPlugin<GeolocationPlugin>('Geolocation', {\n  web: () => import('./web').then((m) => new m.GeolocationWeb()),\n});\n\nexposeSynapse();\n\nexport * from './definitions';\nexport { Geolocation };\n"], "mappings": ";;;;;;AAAA,SAAS,EAAE,GAAG;AACZ,IAAE,eAAe,UAAU,IAAI;AAAA,IAC7B,CAAC;AAAA,IACD;AAAA,MACE,IAAI,GAAG,GAAG;AACR,eAAO,IAAI,MAAM,CAAC,GAAG;AAAA,UACnB,IAAI,GAAG,GAAG;AACR,mBAAO,CAAC,GAAG,GAAG,MAAM;AAClB,oBAAM,IAAI,EAAE,UAAU,QAAQ,CAAC;AAC/B,kBAAI,MAAM,QAAQ;AAChB,kBAAE,IAAI,MAAM,oBAAoB,CAAC,YAAY,CAAC;AAC9C;AAAA,cACF;AACA,kBAAI,OAAO,EAAE,CAAC,KAAK,YAAY;AAC7B,kBAAE,IAAI,MAAM,UAAU,CAAC,kCAAkC,CAAC,EAAE,CAAC;AAC7D;AAAA,cACF;AACA,eAAC,YAAY;AACX,oBAAI;AACF,wBAAM,IAAI,MAAM,EAAE,CAAC,EAAE,CAAC;AACtB,oBAAE,CAAC;AAAA,gBACL,SAAS,GAAG;AACV,oBAAE,CAAC;AAAA,gBACL;AAAA,cACF,GAAG;AAAA,YACL;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AACF;AACA,SAAS,EAAE,GAAG;AACZ,IAAE,eAAe,UAAU,IAAI;AAAA,IAC7B,CAAC;AAAA,IACD;AAAA,MACE,IAAI,GAAG,GAAG;AACR,eAAO,EAAE,QAAQ,QAAQ,CAAC;AAAA,MAC5B;AAAA,IACF;AAAA,EACF;AACF;AACA,SAAS,EAAE,IAAI,OAAI;AACjB,SAAO,SAAS,QAAQ,OAAO,iBAAiB,OAAO,kBAAkB,CAAC,GAAG,OAAO,cAAc,UAAU,CAAC,IAAI,EAAE,MAAM,IAAI,OAAO,YAAY,UAAU,EAAE,MAAM;AACpK;;;ACvCA,IAAM,cAAc,eAAkC,eAAe;EACnE,KAAK,MAAM,OAAO,mBAAO,EAAE,KAAK,CAAC,MAAM,IAAI,EAAE,eAAc,CAAE;CAC9D;AAED,EAAa;", "names": []}