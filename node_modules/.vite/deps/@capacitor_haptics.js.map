{"version": 3, "sources": ["../../@capacitor/haptics/src/index.ts"], "sourcesContent": ["import { registerPlugin } from '@capacitor/core';\n\nimport type { HapticsPlugin } from './definitions';\n\nconst Haptics = registerPlugin<HapticsPlugin>('Haptics', {\n  web: () => import('./web').then(m => new m.HapticsWeb()),\n});\n\nexport * from './definitions';\nexport { Haptics };\n"], "mappings": ";;;;;;;;;;AAIA,IAAM,UAAU,eAA8B,WAAW;EACvD,KAAK,MAAM,OAAO,mBAAO,EAAE,KAAK,OAAK,IAAI,EAAE,WAAU,CAAE;CACxD;", "names": []}