{"version": 3, "sources": ["../../@capacitor/device/src/web.ts"], "sourcesContent": ["import { WebPlugin } from '@capacitor/core';\n\nimport type {\n  BatteryInfo,\n  DeviceId,\n  DeviceInfo,\n  DevicePlugin,\n  GetLanguageCodeResult,\n  LanguageTag,\n} from './definitions';\n\ndeclare global {\n  interface Navigator {\n    getBattery: any;\n    oscpu: any;\n  }\n\n  interface Window {\n    InstallTrigger?: any;\n    ApplePaySession?: any;\n    chrome?: any;\n  }\n}\n\nexport class DeviceWeb extends WebPlugin implements DevicePlugin {\n  async getId(): Promise<DeviceId> {\n    return {\n      identifier: this.getUid(),\n    };\n  }\n\n  async getInfo(): Promise<DeviceInfo> {\n    if (typeof navigator === 'undefined' || !navigator.userAgent) {\n      throw this.unavailable('Device API not available in this browser');\n    }\n\n    const ua = navigator.userAgent;\n    const uaFields = this.parseUa(ua);\n\n    return {\n      model: uaFields.model,\n      platform: 'web' as const,\n      operatingSystem: uaFields.operatingSystem,\n      osVersion: uaFields.osVersion,\n      manufacturer: navigator.vendor,\n      isVirtual: false,\n      webViewVersion: uaFields.browserVersion,\n    };\n  }\n\n  async getBatteryInfo(): Promise<BatteryInfo> {\n    if (typeof navigator === 'undefined' || !navigator.getBattery) {\n      throw this.unavailable('Device API not available in this browser');\n    }\n    let battery: any = {};\n\n    try {\n      battery = await navigator.getBattery();\n    } catch (e) {\n      // Let it fail, we don't care\n    }\n\n    return {\n      batteryLevel: battery.level,\n      isCharging: battery.charging,\n    };\n  }\n\n  async getLanguageCode(): Promise<GetLanguageCodeResult> {\n    return {\n      value: navigator.language.split('-')[0].toLowerCase(),\n    };\n  }\n\n  async getLanguageTag(): Promise<LanguageTag> {\n    return {\n      value: navigator.language,\n    };\n  }\n\n  parseUa(ua: string): any {\n    const uaFields: any = {};\n    const start = ua.indexOf('(') + 1;\n    let end = ua.indexOf(') AppleWebKit');\n    if (ua.indexOf(') Gecko') !== -1) {\n      end = ua.indexOf(') Gecko');\n    }\n    const fields = ua.substring(start, end);\n    if (ua.indexOf('Android') !== -1) {\n      const tmpFields = fields.replace('; wv', '').split('; ').pop();\n      if (tmpFields) {\n        uaFields.model = tmpFields.split(' Build')[0];\n      }\n      uaFields.osVersion = fields.split('; ')[1];\n    } else {\n      uaFields.model = fields.split('; ')[0];\n      if (typeof navigator !== 'undefined' && navigator.oscpu) {\n        uaFields.osVersion = navigator.oscpu;\n      } else {\n        if (ua.indexOf('Windows') !== -1) {\n          uaFields.osVersion = fields;\n        } else {\n          const tmpFields = fields.split('; ').pop();\n          if (tmpFields) {\n            const lastParts = tmpFields\n              .replace(' like Mac OS X', '')\n              .split(' ');\n            uaFields.osVersion = lastParts[lastParts.length - 1].replace(\n              /_/g,\n              '.',\n            );\n          }\n        }\n      }\n    }\n\n    if (/android/i.test(ua)) {\n      uaFields.operatingSystem = 'android';\n    } else if (/iPad|iPhone|iPod/.test(ua) && !window.MSStream) {\n      uaFields.operatingSystem = 'ios';\n    } else if (/Win/.test(ua)) {\n      uaFields.operatingSystem = 'windows';\n    } else if (/Mac/i.test(ua)) {\n      uaFields.operatingSystem = 'mac';\n    } else {\n      uaFields.operatingSystem = 'unknown';\n    }\n\n    // Check for browsers based on non-standard javascript apis, only not user agent\n    const isSafari = !!window.ApplePaySession;\n    const isChrome = !!window.chrome;\n    const isFirefox = /Firefox/.test(ua);\n    const isEdge = /Edg/.test(ua);\n    const isFirefoxIOS = /FxiOS/.test(ua);\n    const isChromeIOS = /CriOS/.test(ua);\n    const isEdgeIOS = /EdgiOS/.test(ua);\n\n    // FF and Edge User Agents both end with \"/MAJOR.MINOR\"\n    if (\n      isSafari ||\n      (isChrome && !isEdge) ||\n      isFirefoxIOS ||\n      isChromeIOS ||\n      isEdgeIOS\n    ) {\n      // Safari version comes as     \"... Version/MAJOR.MINOR ...\"\n      // Chrome version comes as     \"... Chrome/MAJOR.MINOR ...\"\n      // FirefoxIOS version comes as \"... FxiOS/MAJOR.MINOR ...\"\n      // ChromeIOS version comes as  \"... CriOS/MAJOR.MINOR ...\"\n      let searchWord: string;\n      if (isFirefoxIOS) {\n        searchWord = 'FxiOS';\n      } else if (isChromeIOS) {\n        searchWord = 'CriOS';\n      } else if (isEdgeIOS) {\n        searchWord = 'EdgiOS';\n      } else if (isSafari) {\n        searchWord = 'Version';\n      } else {\n        searchWord = 'Chrome';\n      }\n\n      const words = ua.split(' ');\n      for (const word of words) {\n        if (word.includes(searchWord)) {\n          const version = word.split('/')[1];\n          uaFields.browserVersion = version;\n        }\n      }\n    } else if (isFirefox || isEdge) {\n      const reverseUA = ua.split('').reverse().join('');\n      const reverseVersion = reverseUA.split('/')[0];\n      const version = reverseVersion.split('').reverse().join('');\n      uaFields.browserVersion = version;\n    }\n\n    return uaFields;\n  }\n\n  getUid(): string {\n    if (typeof window !== 'undefined' && window.localStorage) {\n      let uid = window.localStorage.getItem('_capuid');\n      if (uid) {\n        return uid;\n      }\n\n      uid = this.uuid4();\n      window.localStorage.setItem('_capuid', uid);\n      return uid;\n    }\n    return this.uuid4();\n  }\n\n  uuid4(): string {\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(\n      /[xy]/g,\n      function (c) {\n        const r = (Math.random() * 16) | 0,\n          v = c === 'x' ? r : (r & 0x3) | 0x8;\n        return v.toString(16);\n      },\n    );\n  }\n}\n"], "mappings": ";;;;;;AAwBM,IAAO,YAAP,cAAyB,UAAS;EACtC,MAAM,QAAK;AACT,WAAO;MACL,YAAY,KAAK,OAAM;;EAE3B;EAEA,MAAM,UAAO;AACX,QAAI,OAAO,cAAc,eAAe,CAAC,UAAU,WAAW;AAC5D,YAAM,KAAK,YAAY,0CAA0C;;AAGnE,UAAM,KAAK,UAAU;AACrB,UAAM,WAAW,KAAK,QAAQ,EAAE;AAEhC,WAAO;MACL,OAAO,SAAS;MAChB,UAAU;MACV,iBAAiB,SAAS;MAC1B,WAAW,SAAS;MACpB,cAAc,UAAU;MACxB,WAAW;MACX,gBAAgB,SAAS;;EAE7B;EAEA,MAAM,iBAAc;AAClB,QAAI,OAAO,cAAc,eAAe,CAAC,UAAU,YAAY;AAC7D,YAAM,KAAK,YAAY,0CAA0C;;AAEnE,QAAI,UAAe,CAAA;AAEnB,QAAI;AACF,gBAAU,MAAM,UAAU,WAAU;aAC7B,GAAG;;AAIZ,WAAO;MACL,cAAc,QAAQ;MACtB,YAAY,QAAQ;;EAExB;EAEA,MAAM,kBAAe;AACnB,WAAO;MACL,OAAO,UAAU,SAAS,MAAM,GAAG,EAAE,CAAC,EAAE,YAAW;;EAEvD;EAEA,MAAM,iBAAc;AAClB,WAAO;MACL,OAAO,UAAU;;EAErB;EAEA,QAAQ,IAAU;AAChB,UAAM,WAAgB,CAAA;AACtB,UAAM,QAAQ,GAAG,QAAQ,GAAG,IAAI;AAChC,QAAI,MAAM,GAAG,QAAQ,eAAe;AACpC,QAAI,GAAG,QAAQ,SAAS,MAAM,IAAI;AAChC,YAAM,GAAG,QAAQ,SAAS;;AAE5B,UAAM,SAAS,GAAG,UAAU,OAAO,GAAG;AACtC,QAAI,GAAG,QAAQ,SAAS,MAAM,IAAI;AAChC,YAAM,YAAY,OAAO,QAAQ,QAAQ,EAAE,EAAE,MAAM,IAAI,EAAE,IAAG;AAC5D,UAAI,WAAW;AACb,iBAAS,QAAQ,UAAU,MAAM,QAAQ,EAAE,CAAC;;AAE9C,eAAS,YAAY,OAAO,MAAM,IAAI,EAAE,CAAC;WACpC;AACL,eAAS,QAAQ,OAAO,MAAM,IAAI,EAAE,CAAC;AACrC,UAAI,OAAO,cAAc,eAAe,UAAU,OAAO;AACvD,iBAAS,YAAY,UAAU;aAC1B;AACL,YAAI,GAAG,QAAQ,SAAS,MAAM,IAAI;AAChC,mBAAS,YAAY;eAChB;AACL,gBAAM,YAAY,OAAO,MAAM,IAAI,EAAE,IAAG;AACxC,cAAI,WAAW;AACb,kBAAM,YAAY,UACf,QAAQ,kBAAkB,EAAE,EAC5B,MAAM,GAAG;AACZ,qBAAS,YAAY,UAAU,UAAU,SAAS,CAAC,EAAE,QACnD,MACA,GAAG;;;;;AAOb,QAAI,WAAW,KAAK,EAAE,GAAG;AACvB,eAAS,kBAAkB;eAClB,mBAAmB,KAAK,EAAE,KAAK,CAAC,OAAO,UAAU;AAC1D,eAAS,kBAAkB;eAClB,MAAM,KAAK,EAAE,GAAG;AACzB,eAAS,kBAAkB;eAClB,OAAO,KAAK,EAAE,GAAG;AAC1B,eAAS,kBAAkB;WACtB;AACL,eAAS,kBAAkB;;AAI7B,UAAM,WAAW,CAAC,CAAC,OAAO;AAC1B,UAAM,WAAW,CAAC,CAAC,OAAO;AAC1B,UAAM,YAAY,UAAU,KAAK,EAAE;AACnC,UAAM,SAAS,MAAM,KAAK,EAAE;AAC5B,UAAM,eAAe,QAAQ,KAAK,EAAE;AACpC,UAAM,cAAc,QAAQ,KAAK,EAAE;AACnC,UAAM,YAAY,SAAS,KAAK,EAAE;AAGlC,QACE,YACC,YAAY,CAAC,UACd,gBACA,eACA,WACA;AAKA,UAAI;AACJ,UAAI,cAAc;AAChB,qBAAa;iBACJ,aAAa;AACtB,qBAAa;iBACJ,WAAW;AACpB,qBAAa;iBACJ,UAAU;AACnB,qBAAa;aACR;AACL,qBAAa;;AAGf,YAAM,QAAQ,GAAG,MAAM,GAAG;AAC1B,iBAAW,QAAQ,OAAO;AACxB,YAAI,KAAK,SAAS,UAAU,GAAG;AAC7B,gBAAM,UAAU,KAAK,MAAM,GAAG,EAAE,CAAC;AACjC,mBAAS,iBAAiB;;;eAGrB,aAAa,QAAQ;AAC9B,YAAM,YAAY,GAAG,MAAM,EAAE,EAAE,QAAO,EAAG,KAAK,EAAE;AAChD,YAAM,iBAAiB,UAAU,MAAM,GAAG,EAAE,CAAC;AAC7C,YAAM,UAAU,eAAe,MAAM,EAAE,EAAE,QAAO,EAAG,KAAK,EAAE;AAC1D,eAAS,iBAAiB;;AAG5B,WAAO;EACT;EAEA,SAAM;AACJ,QAAI,OAAO,WAAW,eAAe,OAAO,cAAc;AACxD,UAAI,MAAM,OAAO,aAAa,QAAQ,SAAS;AAC/C,UAAI,KAAK;AACP,eAAO;;AAGT,YAAM,KAAK,MAAK;AAChB,aAAO,aAAa,QAAQ,WAAW,GAAG;AAC1C,aAAO;;AAET,WAAO,KAAK,MAAK;EACnB;EAEA,QAAK;AACH,WAAO,uCAAuC,QAC5C,SACA,SAAU,GAAC;AACT,YAAM,IAAK,KAAK,OAAM,IAAK,KAAM,GAC/B,IAAI,MAAM,MAAM,IAAK,IAAI,IAAO;AAClC,aAAO,EAAE,SAAS,EAAE;IACtB,CAAC;EAEL;;", "names": []}