{"name": "stripe", "version": "18.2.1", "description": "Stripe API wrapper", "keywords": ["stripe", "payment processing", "credit cards", "api"], "homepage": "https://github.com/stripe/stripe-node", "author": "Stripe <<EMAIL>> (https://stripe.com/)", "contributors": ["Ask <PERSON><PERSON><PERSON><PERSON> <<EMAIL>> (http://www.askask.com/)", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON>"], "repository": {"type": "git", "url": "git://github.com/stripe/stripe-node.git"}, "bugs": "https://github.com/stripe/stripe-node/issues", "engines": {"node": ">=12.*"}, "main": "cjs/stripe.cjs.node.js", "types": "types/index.d.ts", "devDependencies": {"@types/chai": "^4.3.4", "@types/chai-as-promised": "^7.1.5", "@types/mocha": "^10.0.1", "@types/qs": "^6.9.7", "@types/node": ">=12.0.0", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "chai": "^4.3.6", "chai-as-promised": "~7.1.1", "eslint": "^7.32.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-chai-friendly": "^0.7.2", "eslint-plugin-import": "^2.27.5", "eslint-plugin-prettier": "^3.4.1", "mocha": "^8.4.0", "mocha-junit-reporter": "^2.1.0", "nock": "^13.2.9", "node-fetch": "^2.6.7", "nyc": "^15.1.0", "prettier": "^1.16.4", "ts-node": "^10.9.1", "typescript": "^4.9.4", "undici-types": "^7.8.0"}, "resolutions": {"minimist": "1.2.6", "nanoid": "^3.2.0"}, "dependencies": {"qs": "^6.11.0"}, "peerDependencies": {"@types/node": ">=12.x.x"}, "peerDependenciesMeta": {"@types/node": {"optional": true}}, "license": "MIT", "scripts": {"test": "tsc -p tsconfig.cjs.json && mocha", "prepack": "just install && just build"}, "exports": {"types": "./types/index.d.ts", "browser": {"import": "./esm/stripe.esm.worker.js", "require": "./cjs/stripe.cjs.worker.js"}, "bun": {"import": "./esm/stripe.esm.worker.js", "require": "./cjs/stripe.cjs.worker.js"}, "deno": {"import": "./esm/stripe.esm.worker.js", "require": "./cjs/stripe.cjs.worker.js"}, "worker": {"import": "./esm/stripe.esm.worker.js", "require": "./cjs/stripe.cjs.worker.js"}, "workerd": {"import": "./esm/stripe.esm.worker.js", "require": "./cjs/stripe.cjs.worker.js"}, "default": {"import": "./esm/stripe.esm.node.js", "require": "./cjs/stripe.cjs.node.js"}}}